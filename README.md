# Slack Chat History Exporter

A modular Python script that uses the Slack API to retrieve all conversation history accessible to a user via their OAuth Token and stores it in MongoDB. This includes public channels, private channels, direct messages, group messages (MPIMs), and threaded replies.

## Features

- **Complete History Export**: Fetches all accessible conversations and messages
- **Thread Support**: Retrieves and nests threaded replies within parent messages
- **Multiple Conversation Types**: Handles channels, DMs, group messages, and MPIMs
- **MongoDB Storage**: Each conversation stored in its own collection with metadata
- **Rate Limiting**: Respects Slack API rate limits with configurable delays
- **Error Handling**: Comprehensive logging and retry logic
- **Modular Design**: Clean separation of concerns across multiple modules

## Prerequisites

### 1. Slack User OAuth Token

You need a Slack User OAuth Token with the following scopes:

- `channels:history` - Read messages in public channels
- `channels:read` - List public channels
- `groups:history` - Read messages in private channels
- `groups:read` - List private channels
- `im:history` - Read direct messages
- `im:read` - List direct messages
- `mpim:history` - Read group messages
- `mpim:read` - List group messages
- `users:read` - Read user information (for DM naming)

#### Getting Your Token:

1. Go to [https://api.slack.com/apps](https://api.slack.com/apps)
2. Create a new app or use an existing one
3. Go to "OAuth & Permissions"
4. Add the required scopes under "User Token Scopes"
5. Install the app to your workspace
6. Copy the "User OAuth Token" (starts with `xoxp-`)

### 2. MongoDB

You need a MongoDB instance (local or remote). The script will create the database and collections automatically.

#### Local MongoDB:
```bash
# Install MongoDB (example for macOS)
brew install mongodb-community
brew services start mongodb-community

# Or using Docker
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

#### Remote MongoDB:
You can use MongoDB Atlas, AWS DocumentDB, or any other MongoDB service.

## Installation

1. **Clone or download the script files**

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Set up environment variables:**
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env with your actual values
nano .env
```

4. **Configure your .env file:**
```env
SLACK_USER_TOKEN=xoxp-your-actual-token-here
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE_NAME=slack_chat_history
```

## Usage

### Basic Usage

```bash
python main.py
```

### Help

```bash
python main.py --help
```

### Configuration Options

All configuration is done through environment variables in the `.env` file:

| Variable | Default | Description |
|----------|---------|-------------|
| `SLACK_USER_TOKEN` | Required | Your Slack User OAuth Token |
| `MONGODB_CONNECTION_STRING` | `mongodb://localhost:27017/` | MongoDB connection string |
| `MONGODB_DATABASE_NAME` | `slack_chat_history` | Database name to use |
| `RATE_LIMIT_DELAY` | `1.0` | Seconds between API calls |
| `MAX_RETRIES` | `3` | Maximum retry attempts for failed calls |
| `CONVERSATIONS_LIMIT` | `200` | Conversations per API call |
| `MESSAGES_LIMIT` | `200` | Messages per API call |
| `INCLUDE_ARCHIVED` | `false` | Include archived conversations |
| `CONVERSATION_TYPES` | `public_channel,private_channel,mpim,im` | Types to export |
| `LOG_LEVEL` | `INFO` | Logging level (DEBUG, INFO, WARNING, ERROR) |
| `LOG_FILE` | `slack_export.log` | Log file path |

## Data Structure

### MongoDB Collections

The script creates the following structure in MongoDB:

- **Message Collections**: Each conversation gets its own collection
  - Channels: Use channel name (e.g., `general`, `random`)
  - DMs: Use participant names (e.g., `dm_alice_bob`)
  - Group DMs: Use participant names (e.g., `group_dm_alice_bob_charlie`)

- **Metadata Collection**: `_conversation_metadata`
  - Contains conversation details, types, and collection mappings

### Message Document Structure

```json
{
  "ts": "1234567890.123456",
  "type": "message",
  "user": "U1234567890",
  "text": "Hello world!",
  "thread_ts": "1234567890.123456",
  "reply_count": 2,
  "blocks": [...],
  "attachments": [...],
  "files": [...],
  "reactions": [...],
  "thread_replies": [
    {
      "ts": "1234567890.123457",
      "user": "U0987654321",
      "text": "Reply to thread",
      "parent_user_id": "U1234567890"
    }
  ]
}
```

## Module Structure

- **`main.py`** - Main execution script
- **`auth.py`** - Slack authentication and token validation
- **`slack_client.py`** - Slack API client with rate limiting
- **`fetch_conversations.py`** - Conversation fetching orchestrator
- **`mongo_handler.py`** - MongoDB connection and storage
- **`utils.py`** - Utility functions and helpers
- **`config.py`** - Configuration management

## Error Handling

The script includes comprehensive error handling:

- **Rate Limiting**: Automatic retry with exponential backoff
- **API Errors**: Detailed logging and graceful degradation
- **Network Issues**: Retry logic for transient failures
- **Data Validation**: Sanitization of collection names and data

## Monitoring Progress

The script provides detailed logging:

```
2024-01-15 10:30:15 - INFO - Authenticated as: john.doe on team MyCompany
2024-01-15 10:30:16 - INFO - Found 25 conversations to process
2024-01-15 10:30:17 - INFO - Processing conversation 1/25: general
2024-01-15 10:30:20 - INFO - Stored 150 messages for general
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**
   - Check your token is correct and starts with `xoxp-`
   - Verify all required scopes are granted
   - Ensure the app is installed to your workspace

2. **No Conversations Found**
   - Check token permissions
   - Verify you're a member of the conversations you want to export
   - Check if `CONVERSATION_TYPES` includes the types you want

3. **MongoDB Connection Failed**
   - Verify MongoDB is running
   - Check connection string format
   - Ensure network connectivity

4. **Rate Limiting**
   - Increase `RATE_LIMIT_DELAY` in .env
   - The script automatically handles rate limits with retries

### Debug Mode

Enable debug logging for more detailed output:

```env
LOG_LEVEL=DEBUG
```

## Security Notes

- Keep your Slack token secure and never commit it to version control
- Use environment variables or secure secret management
- Consider using a dedicated service account for exports
- Review MongoDB security settings for production use

## License

This script is provided as-is for educational and utility purposes. Please ensure compliance with your organization's data policies and Slack's Terms of Service when exporting conversation data.
