#!/usr/bin/env python3
"""
Slack Chat History Exporter - Main Script

This script exports all accessible Slack conversation history to MongoDB.
It handles public channels, private channels, direct messages, and group messages.
"""
import sys
import logging
from typing import Optional
from auth import create_authenticated_client
from slack_client import SlackConversationClient
from mongo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from fetch_conversations import ConversationFetcher
from utils import setup_logging
from config import LOG_LEVEL, LOG_FILE


def main():
    """Main execution function."""
    # Set up logging
    setup_logging(LOG_LEVEL, LOG_FILE)
    logger = logging.getLogger(__name__)

    logger.info("=== Slack Chat History Exporter Started ===")

    try:
        # Step 1: Authenticate with Slack
        logger.info("Step 1: Authenticating with Slack...")
        slack_client, user_info = create_authenticated_client()
        logger.info(f"Authenticated as: {user_info['user']} on team {user_info['team']}")

        # Step 2: Initialize Slack conversation client
        logger.info("Step 2: Initializing Slack conversation client...")
        conversation_client = SlackConversationClient(slack_client)

        # Step 3: Connect to MongoDB
        logger.info("Step 3: Connecting to MongoDB...")
        with MongoHandler() as mongo_handler:
            logger.info("MongoDB connection established")

            # Step 4: Initialize conversation fetcher
            logger.info("Step 4: Initializing conversation fetcher...")
            fetcher = ConversationFetcher(conversation_client, mongo_handler)

            # Step 5: Fetch all conversations and messages
            logger.info("Step 5: Starting conversation and message fetch...")
            stats = fetcher.fetch_all_conversations()

            # Step 6: Display final results
            logger.info("Step 6: Export completed successfully!")
            display_final_results(stats, mongo_handler)

    except KeyboardInterrupt:
        logger.info("Export interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Export failed with error: {str(e)}", exc_info=True)
        sys.exit(1)

    logger.info("=== Slack Chat History Exporter Completed ===")


def display_final_results(stats: dict, mongo_handler: MongoHandler):
    """
    Display final export results.

    Args:
        stats: Processing statistics
        mongo_handler: MongoDB handler for additional stats
    """
    logger = logging.getLogger(__name__)

    print("\n" + "="*60)
    print("SLACK CHAT HISTORY EXPORT COMPLETED")
    print("="*60)
    print(f"Conversations processed: {stats['conversations_processed']}")
    print(f"Messages stored: {stats['messages_stored']}")
    print(f"Threads processed: {stats['threads_processed']}")
    print(f"Errors encountered: {stats['errors']}")

    # Database information
    try:
        if mongo_handler.database is not None:
            collections = mongo_handler.database.list_collection_names()
            message_collections = [c for c in collections if not c.startswith('_')]

            print(f"Collections created: {len(message_collections)}")
            print(f"Database: {mongo_handler.database_name}")

            # Show some example collections
            if message_collections:
                print("\nSample collections:")
                for collection_name in message_collections[:10]:
                    stats_info = mongo_handler.get_conversation_stats(collection_name)
                    if stats_info:
                        print(f"  - {collection_name}: {stats_info['total_messages']} messages")

                if len(message_collections) > 10:
                    print(f"  ... and {len(message_collections) - 10} more collections")

    except Exception as e:
        logger.warning(f"Could not display database statistics: {str(e)}")

    print("\nTo access your data:")
    print(f"1. Connect to MongoDB: {mongo_handler.connection_string}")
    print(f"2. Use database: {mongo_handler.database_name}")
    print("3. Each conversation is stored in its own collection")
    print("4. Check '_conversation_metadata' collection for conversation details")
    print("="*60)


def check_prerequisites():
    """Check if all prerequisites are met."""
    logger = logging.getLogger(__name__)

    try:
        # Check if required environment variables are set
        from config import SLACK_USER_TOKEN, MONGODB_CONNECTION_STRING

        if not SLACK_USER_TOKEN:
            logger.error("SLACK_USER_TOKEN environment variable is not set")
            return False

        if not MONGODB_CONNECTION_STRING:
            logger.error("MONGODB_CONNECTION_STRING environment variable is not set")
            return False

        return True

    except Exception as e:
        logger.error(f"Error checking prerequisites: {str(e)}")
        return False


def print_usage():
    """Print usage information."""
    print("""
Slack Chat History Exporter

This script exports all accessible Slack conversation history to MongoDB.

Prerequisites:
1. Slack User OAuth Token with required scopes:
   - channels:history, channels:read
   - groups:history, groups:read
   - im:history, im:read
   - mpim:history, mpim:read
   - users:read

2. MongoDB instance (local or remote)

3. Environment variables (create a .env file):
   SLACK_USER_TOKEN=xoxp-your-user-token
   MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
   MONGODB_DATABASE_NAME=slack_chat_history

Usage:
   python main.py

The script will:
1. Authenticate with Slack
2. Connect to MongoDB
3. Fetch all conversations (channels, DMs, group messages)
4. Export all messages including threaded replies
5. Store data in MongoDB collections

Each conversation type gets its own collection:
- Channels: Use channel name
- DMs: Use participant names (e.g., dm_user1_user2)
- Group messages: Use participant names or MPIM name

For more configuration options, see config.py
""")


if __name__ == "__main__":
    # Check for help flag
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        print_usage()
        sys.exit(0)

    # Check prerequisites before starting
    if not check_prerequisites():
        print("\nPrerequisites not met. Use --help for setup instructions.")
        sys.exit(1)

    # Run main export process
    main()
