2025-05-26 08:10:11,445 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:10:11,445 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:10:11,445 - auth - INFO - Testing Slack authentication...
2025-05-26 08:10:11,855 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:10:12,221 - auth - WARNING - Could not fetch team info: The request to the Slack API failed. (url: https://www.slack.com/api/team.info)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'team:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,mpim:read,users:read'}
2025-05-26 08:10:12,604 - auth - INFO - Validating token permissions...
2025-05-26 08:10:12,991 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:10:13,371 - auth - INFO - ✓ Users access validated
2025-05-26 08:10:13,372 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:10:13,372 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:10:13,372 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:10:13,372 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:10:13,392 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:10:13,393 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:10:13,393 - __main__ - INFO - MongoDB connection established
2025-05-26 08:10:13,393 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:10:13,393 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:10:13,393 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:10:13,393 - slack_client - INFO - Fetching conversations...
2025-05-26 08:10:14,787 - slack_client - ERROR - Slack API error fetching conversations: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.list)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,mpim:read,users:read'}
2025-05-26 08:10:14,788 - slack_client - INFO - Finished fetching conversations. Total: 0
2025-05-26 08:10:14,788 - fetch_conversations - INFO - Found 0 conversations to process
2025-05-26 08:10:14,788 - fetch_conversations - WARNING - No conversations found. Check token permissions.
2025-05-26 08:10:14,788 - __main__ - INFO - Step 6: Export completed successfully!
2025-05-26 08:10:14,788 - __main__ - WARNING - Could not display database statistics: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:10:14,790 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:10:14,791 - __main__ - INFO - === Slack Chat History Exporter Completed ===
2025-05-26 08:14:08,461 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:14:08,461 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:14:08,461 - auth - INFO - Testing Slack authentication...
2025-05-26 08:14:08,858 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:14:09,225 - auth - WARNING - Could not fetch team info: The request to the Slack API failed. (url: https://www.slack.com/api/team.info)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'team:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,users:read'}
2025-05-26 08:14:09,598 - auth - INFO - Validating token permissions...
2025-05-26 08:14:09,987 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:14:10,406 - auth - INFO - ✓ Users access validated
2025-05-26 08:14:10,406 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:14:10,406 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:14:10,407 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:14:10,407 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:14:10,424 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:14:10,424 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:14:10,424 - __main__ - INFO - MongoDB connection established
2025-05-26 08:14:10,424 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:14:10,425 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:14:10,425 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:14:10,425 - slack_client - INFO - Fetching conversations...
2025-05-26 08:14:12,047 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 08:14:13,612 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 08:14:15,152 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 08:14:16,720 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 08:14:18,503 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 08:14:20,120 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 08:14:21,715 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 08:14:21,716 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 08:14:21,716 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 08:14:21,716 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 08:14:21,716 - mongo_handler - ERROR - Error storing conversation metadata: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:25,655 - mongo_handler - ERROR - Error storing message 1747396902.303859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:27,105 - mongo_handler - ERROR - Error storing message 1744355200.762669: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:28,522 - mongo_handler - ERROR - Error storing message 1744192532.724229: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:29,968 - mongo_handler - ERROR - Error storing message 1744178063.003639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:31,397 - mongo_handler - ERROR - Error storing message 1744099830.353349: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:32,897 - mongo_handler - ERROR - Error storing message 1743347915.465569: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:34,354 - mongo_handler - ERROR - Error storing message 1743210338.135909: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:35,776 - mongo_handler - ERROR - Error storing message 1743166055.477609: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:37,219 - mongo_handler - ERROR - Error storing message 1743157914.096409: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:38,628 - mongo_handler - ERROR - Error storing message 1743150709.311439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:40,050 - mongo_handler - ERROR - Error storing message 1743147488.320129: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:41,470 - mongo_handler - ERROR - Error storing message 1743147475.562899: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:43,009 - mongo_handler - ERROR - Error storing message 1742286301.266799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:44,404 - mongo_handler - ERROR - Error storing message 1738570485.991439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:45,909 - mongo_handler - ERROR - Error storing message 1736914663.991389: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:47,330 - mongo_handler - ERROR - Error storing message 1734334554.318679: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:48,878 - mongo_handler - ERROR - Error storing message 1732514609.606029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:50,325 - mongo_handler - ERROR - Error storing message 1732250863.861029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:51,739 - mongo_handler - ERROR - Error storing message 1732087187.085749: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:53,153 - mongo_handler - ERROR - Error storing message 1732014089.175779: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:54,614 - mongo_handler - ERROR - Error storing message 1732005170.304639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:56,031 - mongo_handler - ERROR - Error storing message 1731661055.251969: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:57,431 - mongo_handler - ERROR - Error storing message 1731648795.418259: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:58,894 - mongo_handler - ERROR - Error storing message 1731492774.046459: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:58,894 - mongo_handler - ERROR - Error in batch storing messages: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:00,298 - mongo_handler - ERROR - Error storing message 1731056278.519159: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:01,728 - mongo_handler - ERROR - Error storing message 1730798098.149949: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:03,152 - mongo_handler - ERROR - Error storing message 1730699971.380489: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:04,675 - mongo_handler - ERROR - Error storing message 1730101840.962799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:06,134 - mongo_handler - ERROR - Error storing message 1729588299.085969: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:07,580 - mongo_handler - ERROR - Error storing message 1729572741.334489: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:08,991 - mongo_handler - ERROR - Error storing message 1729223894.443859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:10,424 - mongo_handler - ERROR - Error storing message 1728287609.070269: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:11,885 - mongo_handler - ERROR - Error storing message 1726805841.094529: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:13,310 - mongo_handler - ERROR - Error storing message 1726126388.287439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:14,711 - mongo_handler - ERROR - Error storing message 1725275056.053199: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:16,136 - mongo_handler - ERROR - Error storing message 1724320650.798829: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:17,646 - mongo_handler - ERROR - Error storing message 1723812130.882189: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:19,035 - mongo_handler - ERROR - Error storing message 1723700030.016269: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:20,463 - mongo_handler - ERROR - Error storing message 1721291134.399779: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:21,957 - mongo_handler - ERROR - Error storing message 1721289718.368819: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:23,409 - mongo_handler - ERROR - Error storing message 1721200735.856689: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:24,870 - mongo_handler - ERROR - Error storing message 1720088648.385649: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:26,302 - mongo_handler - ERROR - Error storing message 1718178346.320819: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:27,503 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:15:27,503 - __main__ - INFO - Export interrupted by user
2025-05-26 08:17:33,504 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:17:33,504 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:17:33,504 - auth - INFO - Testing Slack authentication...
2025-05-26 08:17:33,899 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:17:34,286 - auth - INFO - Team: Invitrace (invitraceworkspace)
2025-05-26 08:17:34,664 - auth - INFO - Validating token permissions...
2025-05-26 08:17:35,066 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:17:35,456 - auth - INFO - ✓ Users access validated
2025-05-26 08:17:35,456 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:17:35,456 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:17:35,456 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:17:35,456 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:17:35,472 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:17:35,472 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:17:35,472 - __main__ - INFO - MongoDB connection established
2025-05-26 08:17:35,472 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:17:35,472 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:17:35,472 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:17:35,472 - slack_client - INFO - Fetching conversations...
2025-05-26 08:17:37,064 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 08:17:38,538 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 08:17:39,980 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 08:17:41,434 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 08:17:43,012 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 08:17:44,462 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 08:17:46,011 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 08:17:46,011 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 08:17:46,011 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 08:17:46,011 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 08:17:46,011 - mongo_handler - ERROR - Error storing conversation metadata: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:49,581 - mongo_handler - ERROR - Error storing message 1747396902.303859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:51,070 - mongo_handler - ERROR - Error storing message 1744355200.762669: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:52,855 - mongo_handler - ERROR - Error storing message 1744192532.724229: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:54,323 - mongo_handler - ERROR - Error storing message 1744178063.003639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:55,985 - mongo_handler - ERROR - Error storing message 1744099830.353349: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:57,484 - mongo_handler - ERROR - Error storing message 1743347915.465569: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:58,944 - mongo_handler - ERROR - Error storing message 1743210338.135909: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:00,371 - mongo_handler - ERROR - Error storing message 1743166055.477609: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:01,768 - mongo_handler - ERROR - Error storing message 1743157914.096409: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:03,419 - mongo_handler - ERROR - Error storing message 1743150709.311439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:04,852 - mongo_handler - ERROR - Error storing message 1743147488.320129: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:06,282 - mongo_handler - ERROR - Error storing message 1743147475.562899: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:08,048 - mongo_handler - ERROR - Error storing message 1742286301.266799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:09,585 - mongo_handler - ERROR - Error storing message 1738570485.991439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:11,046 - mongo_handler - ERROR - Error storing message 1736914663.991389: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:12,476 - mongo_handler - ERROR - Error storing message 1734334554.318679: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:13,879 - mongo_handler - ERROR - Error storing message 1732514609.606029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:15,292 - mongo_handler - ERROR - Error storing message 1732250863.861029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:16,710 - mongo_handler - ERROR - Error storing message 1732087187.085749: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:16,902 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:18:16,902 - __main__ - INFO - Export interrupted by user
