2025-05-26 08:10:11,445 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:10:11,445 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:10:11,445 - auth - INFO - Testing Slack authentication...
2025-05-26 08:10:11,855 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:10:12,221 - auth - WARNING - Could not fetch team info: The request to the Slack API failed. (url: https://www.slack.com/api/team.info)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'team:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,mpim:read,users:read'}
2025-05-26 08:10:12,604 - auth - INFO - Validating token permissions...
2025-05-26 08:10:12,991 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:10:13,371 - auth - INFO - ✓ Users access validated
2025-05-26 08:10:13,372 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:10:13,372 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:10:13,372 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:10:13,372 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:10:13,392 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:10:13,393 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:10:13,393 - __main__ - INFO - MongoDB connection established
2025-05-26 08:10:13,393 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:10:13,393 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:10:13,393 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:10:13,393 - slack_client - INFO - Fetching conversations...
2025-05-26 08:10:14,787 - slack_client - ERROR - Slack API error fetching conversations: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.list)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,mpim:read,users:read'}
2025-05-26 08:10:14,788 - slack_client - INFO - Finished fetching conversations. Total: 0
2025-05-26 08:10:14,788 - fetch_conversations - INFO - Found 0 conversations to process
2025-05-26 08:10:14,788 - fetch_conversations - WARNING - No conversations found. Check token permissions.
2025-05-26 08:10:14,788 - __main__ - INFO - Step 6: Export completed successfully!
2025-05-26 08:10:14,788 - __main__ - WARNING - Could not display database statistics: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:10:14,790 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:10:14,791 - __main__ - INFO - === Slack Chat History Exporter Completed ===
2025-05-26 08:14:08,461 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:14:08,461 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:14:08,461 - auth - INFO - Testing Slack authentication...
2025-05-26 08:14:08,858 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:14:09,225 - auth - WARNING - Could not fetch team info: The request to the Slack API failed. (url: https://www.slack.com/api/team.info)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'team:read', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,users:read'}
2025-05-26 08:14:09,598 - auth - INFO - Validating token permissions...
2025-05-26 08:14:09,987 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:14:10,406 - auth - INFO - ✓ Users access validated
2025-05-26 08:14:10,406 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:14:10,406 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:14:10,407 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:14:10,407 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:14:10,424 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:14:10,424 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:14:10,424 - __main__ - INFO - MongoDB connection established
2025-05-26 08:14:10,424 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:14:10,425 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:14:10,425 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:14:10,425 - slack_client - INFO - Fetching conversations...
2025-05-26 08:14:12,047 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 08:14:13,612 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 08:14:15,152 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 08:14:16,720 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 08:14:18,503 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 08:14:20,120 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 08:14:21,715 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 08:14:21,716 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 08:14:21,716 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 08:14:21,716 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 08:14:21,716 - mongo_handler - ERROR - Error storing conversation metadata: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:25,655 - mongo_handler - ERROR - Error storing message 1747396902.303859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:27,105 - mongo_handler - ERROR - Error storing message 1744355200.762669: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:28,522 - mongo_handler - ERROR - Error storing message 1744192532.724229: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:29,968 - mongo_handler - ERROR - Error storing message 1744178063.003639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:31,397 - mongo_handler - ERROR - Error storing message 1744099830.353349: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:32,897 - mongo_handler - ERROR - Error storing message 1743347915.465569: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:34,354 - mongo_handler - ERROR - Error storing message 1743210338.135909: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:35,776 - mongo_handler - ERROR - Error storing message 1743166055.477609: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:37,219 - mongo_handler - ERROR - Error storing message 1743157914.096409: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:38,628 - mongo_handler - ERROR - Error storing message 1743150709.311439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:40,050 - mongo_handler - ERROR - Error storing message 1743147488.320129: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:41,470 - mongo_handler - ERROR - Error storing message 1743147475.562899: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:43,009 - mongo_handler - ERROR - Error storing message 1742286301.266799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:44,404 - mongo_handler - ERROR - Error storing message 1738570485.991439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:45,909 - mongo_handler - ERROR - Error storing message 1736914663.991389: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:47,330 - mongo_handler - ERROR - Error storing message 1734334554.318679: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:48,878 - mongo_handler - ERROR - Error storing message 1732514609.606029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:50,325 - mongo_handler - ERROR - Error storing message 1732250863.861029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:51,739 - mongo_handler - ERROR - Error storing message 1732087187.085749: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:53,153 - mongo_handler - ERROR - Error storing message 1732014089.175779: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:54,614 - mongo_handler - ERROR - Error storing message 1732005170.304639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:56,031 - mongo_handler - ERROR - Error storing message 1731661055.251969: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:57,431 - mongo_handler - ERROR - Error storing message 1731648795.418259: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:58,894 - mongo_handler - ERROR - Error storing message 1731492774.046459: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:14:58,894 - mongo_handler - ERROR - Error in batch storing messages: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:00,298 - mongo_handler - ERROR - Error storing message 1731056278.519159: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:01,728 - mongo_handler - ERROR - Error storing message 1730798098.149949: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:03,152 - mongo_handler - ERROR - Error storing message 1730699971.380489: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:04,675 - mongo_handler - ERROR - Error storing message 1730101840.962799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:06,134 - mongo_handler - ERROR - Error storing message 1729588299.085969: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:07,580 - mongo_handler - ERROR - Error storing message 1729572741.334489: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:08,991 - mongo_handler - ERROR - Error storing message 1729223894.443859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:10,424 - mongo_handler - ERROR - Error storing message 1728287609.070269: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:11,885 - mongo_handler - ERROR - Error storing message 1726805841.094529: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:13,310 - mongo_handler - ERROR - Error storing message 1726126388.287439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:14,711 - mongo_handler - ERROR - Error storing message 1725275056.053199: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:16,136 - mongo_handler - ERROR - Error storing message 1724320650.798829: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:17,646 - mongo_handler - ERROR - Error storing message 1723812130.882189: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:19,035 - mongo_handler - ERROR - Error storing message 1723700030.016269: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:20,463 - mongo_handler - ERROR - Error storing message 1721291134.399779: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:21,957 - mongo_handler - ERROR - Error storing message 1721289718.368819: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:23,409 - mongo_handler - ERROR - Error storing message 1721200735.856689: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:24,870 - mongo_handler - ERROR - Error storing message 1720088648.385649: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:26,302 - mongo_handler - ERROR - Error storing message 1718178346.320819: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:15:27,503 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:15:27,503 - __main__ - INFO - Export interrupted by user
2025-05-26 08:17:33,504 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:17:33,504 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:17:33,504 - auth - INFO - Testing Slack authentication...
2025-05-26 08:17:33,899 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:17:34,286 - auth - INFO - Team: Invitrace (invitraceworkspace)
2025-05-26 08:17:34,664 - auth - INFO - Validating token permissions...
2025-05-26 08:17:35,066 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:17:35,456 - auth - INFO - ✓ Users access validated
2025-05-26 08:17:35,456 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:17:35,456 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:17:35,456 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:17:35,456 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:17:35,472 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:17:35,472 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:17:35,472 - __main__ - INFO - MongoDB connection established
2025-05-26 08:17:35,472 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:17:35,472 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:17:35,472 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:17:35,472 - slack_client - INFO - Fetching conversations...
2025-05-26 08:17:37,064 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 08:17:38,538 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 08:17:39,980 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 08:17:41,434 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 08:17:43,012 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 08:17:44,462 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 08:17:46,011 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 08:17:46,011 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 08:17:46,011 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 08:17:46,011 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 08:17:46,011 - mongo_handler - ERROR - Error storing conversation metadata: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:49,581 - mongo_handler - ERROR - Error storing message 1747396902.303859: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:51,070 - mongo_handler - ERROR - Error storing message 1744355200.762669: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:52,855 - mongo_handler - ERROR - Error storing message 1744192532.724229: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:54,323 - mongo_handler - ERROR - Error storing message 1744178063.003639: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:55,985 - mongo_handler - ERROR - Error storing message 1744099830.353349: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:57,484 - mongo_handler - ERROR - Error storing message 1743347915.465569: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:17:58,944 - mongo_handler - ERROR - Error storing message 1743210338.135909: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:00,371 - mongo_handler - ERROR - Error storing message 1743166055.477609: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:01,768 - mongo_handler - ERROR - Error storing message 1743157914.096409: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:03,419 - mongo_handler - ERROR - Error storing message 1743150709.311439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:04,852 - mongo_handler - ERROR - Error storing message 1743147488.320129: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:06,282 - mongo_handler - ERROR - Error storing message 1743147475.562899: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:08,048 - mongo_handler - ERROR - Error storing message 1742286301.266799: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:09,585 - mongo_handler - ERROR - Error storing message 1738570485.991439: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:11,046 - mongo_handler - ERROR - Error storing message 1736914663.991389: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:12,476 - mongo_handler - ERROR - Error storing message 1734334554.318679: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:13,879 - mongo_handler - ERROR - Error storing message 1732514609.606029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:15,292 - mongo_handler - ERROR - Error storing message 1732250863.861029: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:16,710 - mongo_handler - ERROR - Error storing message 1732087187.085749: Database objects do not implement truth value testing or bool(). Please compare with None instead: database is not None
2025-05-26 08:18:16,902 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 08:18:16,902 - __main__ - INFO - Export interrupted by user
2025-05-26 08:27:13,456 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 08:27:13,457 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 08:27:13,457 - auth - INFO - Testing Slack authentication...
2025-05-26 08:27:13,859 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 08:27:14,225 - auth - INFO - Team: Invitrace (invitraceworkspace)
2025-05-26 08:27:14,596 - auth - INFO - Validating token permissions...
2025-05-26 08:27:14,988 - auth - INFO - ✓ Conversations access validated
2025-05-26 08:27:15,382 - auth - INFO - ✓ Users access validated
2025-05-26 08:27:15,382 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 08:27:15,382 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 08:27:15,383 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 08:27:15,383 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 08:27:15,397 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 08:27:15,397 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 08:27:15,397 - __main__ - INFO - MongoDB connection established
2025-05-26 08:27:15,397 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 08:27:15,397 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 08:27:15,397 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 08:27:15,397 - slack_client - INFO - Fetching conversations...
2025-05-26 08:27:16,990 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 08:27:18,563 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 08:27:20,204 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 08:27:21,699 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 08:27:23,176 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 08:27:24,632 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 08:27:26,173 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 08:27:26,173 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 08:27:26,173 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 08:27:26,173 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 08:28:04,137 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1747999260.046389'}, 'replacement': {'ts': '1747999260.046389', 'type': 'message', 'user': 'U055Q3F8N4V', 'text': ':tada: *GAMES DAY: Spyfall Night!* :male-detective:\n มาเล่น มาหัวเราะ มาลุ้นกันให้สุด! :game_die::beers::gift:\n\n:date: *วันอังคารที่ 27 พ.ค. 2025*\n:clock530: *เวลา* *17:30 - 19:30น.*\n:round_pushpin: *Pantry Area*\n\nเกมถามตอบวัดไหวพริบ ใครคือสปายที่แอบแฝงตัวอยู่ในหมู่เรา? ใครจะโป๊ะแตกก่อนกัน?\n:pizza: *มีของกินเล่นให้พร้อม*\n :beer: *เบียร์เย็น ๆ ไว้คลายเครียด (หรือเพิ่มความกล้า!)*\n :gift: *ลุ้นรับของรางวัลเล็ก ๆ น่ารักๆ สำหรับผู้ชนะและผู้เล่นสายฮา!*\n\n:pushpin: ไม่ต้องมีพื้นฐาน เล่นง่าย มาได้เลยทุกคน!\n แวะมาเจอกันหลังเลิกงาน ชวนเพื่อน ชวนทีม มาสนุกด้วยกัน!\n*แล้วเจอกันที่ Pantry!* :partying_face:\n\n_*วิธีเล่น*_ <https://www.youtube.com/watch?v=uqIgtqdUOZI>\n<!here>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'c1swo', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'tada', 'unicode': '1f389'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'GAMES DAY: Spyfall Night!', 'style': {'bold': True}}, {'type': 'text', 'text': ' '}, {'type': 'emoji', 'name': 'male-detective', 'unicode': '1f575-fe0f-200d-2642-fe0f'}, {'type': 'text', 'text': '\n มาเล่น มาหัวเราะ มาลุ้นกันให้สุด! '}, {'type': 'emoji', 'name': 'game_die', 'unicode': '1f3b2'}, {'type': 'emoji', 'name': 'beers', 'unicode': '1f37b'}, {'type': 'emoji', 'name': 'gift', 'unicode': '1f381'}, {'type': 'text', 'text': '\n\n'}, {'type': 'emoji', 'name': 'date', 'unicode': '1f4c5'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'วันอังคารที่ 27 พ.ค. 2025', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}, {'type': 'emoji', 'name': 'clock530', 'unicode': '1f560'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'เวลา', 'style': {'bold': True}}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': '17:30 - 19:30น.', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}, {'type': 'emoji', 'name': 'round_pushpin', 'unicode': '1f4cd'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'Pantry Area', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\nเกมถามตอบวัดไหวพริบ ใครคือสปายที่แอบแฝงตัวอยู่ในหมู่เรา? ใครจะโป๊ะแตกก่อนกัน?\n'}, {'type': 'emoji', 'name': 'pizza', 'unicode': '1f355'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'มีของกินเล่นให้พร้อม', 'style': {'bold': True}}, {'type': 'text', 'text': '\n '}, {'type': 'emoji', 'name': 'beer', 'unicode': '1f37a'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'เบียร์เย็น ๆ ไว้คลายเครียด (หรือเพิ่มความกล้า!)', 'style': {'bold': True}}, {'type': 'text', 'text': '\n '}, {'type': 'emoji', 'name': 'gift', 'unicode': '1f381'}, {'type': 'text', 'text': ' '}, {'type': 'text', 'text': 'ลุ้นรับของรางวัลเล็ก ๆ น่ารักๆ สำหรับผู้ชนะและผู้เล่นสายฮา!', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\n'}, {'type': 'emoji', 'name': 'pushpin', 'unicode': '1f4cc'}, {'type': 'text', 'text': ' ไม่ต้องมีพื้นฐาน เล่นง่าย มาได้เลยทุกคน!\n แวะมาเจอกันหลังเลิกงาน ชวนเพื่อน ชวนทีม มาสนุกด้วยกัน!\n'}, {'type': 'text', 'text': 'แล้วเจอกันที่ Pantry!', 'style': {'bold': True}}, {'type': 'text', 'text': ' '}, {'type': 'emoji', 'name': 'partying_face', 'unicode': '1f973'}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'วิธีเล่น', 'style': {'bold': True, 'italic': True}}, {'type': 'text', 'text': ' '}, {'type': 'link', 'url': 'https://www.youtube.com/watch?v=uqIgtqdUOZI'}, {'type': 'text', 'text': '\n'}, {'type': 'broadcast', 'range': 'here'}]}]}], 'attachments': [], 'files': [{'id': 'F08TNFHNDDK', 'created': 1747999172, 'timestamp': 1747999172, 'name': 'Games Day 27 May 25.jpg', 'title': 'Games Day 27 May 25.jpg', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U055Q3F8N4V', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 596894, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F08TNFHNDDK/games_day_27_may_25.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F08TNFHNDDK/download/games_day_27_may_25.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 800, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08TNFHNDDK-efdc59b076/games_day_27_may_25_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 1024, 'original_w': 1080, 'original_h': 1080, 'thumb_tiny': 'AwAwADC1JIUIAXOabKQ0IOOvNOk2bgGUk9sCmuQYRtGASNtObsiluQPAPlwcE9qjMMinpn6VdJCjJ7U1AG5PJBzWRRFa7w5BUgEdcdKtAH1/SmqDsOOp5FEalVwxya2jsSwdFdxuAJA6GiUqsPTK9BioHcydQB9KcgaVgrHiqcdBCgLIoJ60m0Idu7aGHX0qbyFCnYSCPeqjZb7xrJQvqirlpWDodh6DFEKspO45GBUayIsZVVK5FOilUAhjjmtLNIkRUUABsZpu8RykAZx1pJcqSCGGQeB3pCyZBEffnIHSpSbd2O5M06kYUYJ7moeF6kU0Muw5T5seg9KUsu9WVcAdRgVotNkIcCOxU0jY29RSKy5G8F8DqQKT5doI68cenFNPyA//2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F08TNFHNDDK/games_day_27_may_25.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F08TNFHNDDK-414b2a85e5', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': 'fast_parrot', 'users': ['U04EXRJ3BHQ', 'U087R5JTPUZ', 'U06UNELKMC3', 'U04BBQ80F2B', 'U072NP1567R', 'U06N270KHHP', 'U07A0JBTJH5'], 'count': 7}, {'name': '02_cheer', 'users': ['U04EXRJ3BHQ', 'U076E0UJEJE', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 5}, {'name': 'meow_party', 'users': ['U04EXRJ3BHQ', 'U076E0UJEJE', 'U055Q3F8N4V', 'U087R5JTPUZ', 'U06UNELKMC3', 'U04BBQ80F2B', 'U07LGEDLCJW', 'U07A0JBTJH5'], 'count': 8}, {'name': 'aaw_yeah', 'users': ['U055Q3F8N4V', 'U087R5JTPUZ', 'U04EXRJ3BHQ', 'U04BBQ80F2B', 'U07LGEDLCJW', 'U055VG12Z1A', 'U07A0JBTJH5'], 'count': 7}, {'name': 'zany_face', 'users': ['U04EXRJ3BHQ', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 4}, {'name': '1611481030740', 'users': ['U055Q3F8N4V', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 4}, {'name': 'everythings_fine_parrot', 'users': ['U04EXRJ3BHQ', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5', 'U04RKNHDWBX'], 'count': 5}, {'name': 'cool-doge', 'users': ['U04EXRJ3BHQ', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 4}, {'name': 'ae', 'users': ['U055Q3F8N4V', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 4}, {'name': 'blob-dance', 'users': ['U055Q3F8N4V', 'U087R5JTPUZ', 'U04BBQ80F2B', 'U07A0JBTJH5'], 'count': 4}, {'name': 'gorilla', 'users': ['U07A0JBTJH5'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:28:49,007 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1731296126.864989'}, 'replacement': {'ts': '1731296126.864989', 'type': 'message', 'user': 'U04BC088399', 'text': 'Why did you skip Loy Krathong', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '/pMoc', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Why did you skip Loy Krathong'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:29:06,251 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1703245299.114319'}, 'replacement': {'ts': '1703245299.114319', 'type': 'message', 'user': 'U055Q3F8N4V', 'text': '<https://forms.gle/QXUrQKdAqv6GgogH7>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '9XYkp', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://forms.gle/QXUrQKdAqv6GgogH7'}]}]}], 'attachments': [{'from_url': 'https://forms.gle/QXUrQKdAqv6GgogH7', 'image_url': 'https://lh5.googleusercontent.com/yMCva4_a3eL9VjQXeltYhNpy6E-FaQAPdWKpUC4NT12Paw9_YCGgBKBmtU7LRr78uZ-qrxVgtYU', 'image_width': 800, 'image_height': 600, 'image_bytes': 52803, 'service_icon': 'http://ssl.gstatic.com/docs/forms/device_home/ios_120.png', 'id': 1, 'original_url': 'https://forms.gle/QXUrQKdAqv6GgogH7', 'fallback': 'Google Docs: สวัสดี นี่คือโพล การแต่งกาย ประจำปี 2023', 'text': '- แบบสอบถามนี้เพื่อความสนุกเท่านั้น\n- แบบสอบถามนี้ไม่ระบุตัวตนผู้ตอบ สบายใจได้!\n- ห้ามตอบตัวเอง!\n- กรุณาตอบตามความเป็นจริง!', 'title': 'สวัสดี นี่คือโพล การแต่งกาย ประจำปี 2023', 'title_link': 'https://forms.gle/QXUrQKdAqv6GgogH7', 'service_name': 'Google Docs'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:29:30,566 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1702613261.949459'}, 'replacement': {'ts': '1702613261.949459', 'type': 'message', 'user': 'U04RNH03CHH', 'text': 'Yey!!! it’s FRIDAY thx u buddy krub :zany_face: ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'pX3nF', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Yey!!! '}, {'type': 'text', 'text': 'it’s'}, {'type': 'text', 'text': ' FRIDAY thx u buddy krub '}, {'type': 'emoji', 'name': 'zany_face', 'unicode': '1f92a'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [{'id': 'F069VEVSTGX', 'created': 1702613077, 'timestamp': 1702613077, 'name': 'IMG_8117.jpg', 'title': 'IMG_8117', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04RNH03CHH', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 742009, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F069VEVSTGX/img_8117.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F069VEVSTGX/download/img_8117.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F069VEVSTGX-1719b69a75/img_8117_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 3024, 'original_h': 4032, 'thumb_tiny': 'AwAwACRkd15kqDYRg5POe1NlcTygxgk46Himrth5Knmh1DEsqMrHnNO4cvUYx2HDDBpmc9KGTuM8+1IQQO4/Ci4NNbj9rjGVPIzRhv7pqZ0kyACAAoA/Km7JP7w/KgRLNklAyYGfXNLipLjBVcYyGBpmBiqsOLuiKVsEevr6UqAldxDtn15psmC/QAgdatQEeUtZ3NJW2IGZixJDf98//XpMn0b/AL5/+vV4cilxSuRYjdFZCvT6UwQj+8alNNxTYIia2G7IbFPRNoAz0p2KD0qRlVrzBwq8e5o+2n+6Pzqs4wxptXZE3Z//2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F069VEVSTGX/img_8117.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F069VEVSTGX-fc1dfda629', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:29:52,157 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1701927091.377929'}, 'replacement': {'ts': '1701927091.377929', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'Thx u Buddy ค่า\nรู้ได้ไง ว่าชอบ ชาคาโมมายล มากกก', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'LLYfG', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Thx u Buddy ค่า\nรู้ได้ไง ว่าชอบ ชาคาโมมายล มากกก'}]}]}], 'attachments': [], 'files': [{'id': 'F068M15KE15', 'created': 1701927059, 'timestamp': 1701927059, 'name': 'IMG_4719.jpg', 'title': 'IMG_4719', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20VDG8', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 1306326, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F068M15KE15/img_4719.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F068M15KE15/download/img_4719.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F068M15KE15-36f8980e68/img_4719_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 3024, 'original_h': 4032, 'thumb_tiny': 'AwAwACSr9w8E/nSi4ZQQAMHr3qdLRGTcS3PvTvsUfq/51Lkuo7ECsjDAQA+3FMZAOcEfjVsWSLyGcH60G0TGCz4o5kFiFLuRFCjGB04p322T/IpskARsZOO1N8se9O6CzNEJhRjGMUDg44zVVXUrtkLDHQij9z/z0k/M1PKmF2Wju9gKMEjHGKrK8SZw7Nn+9T/OjyOfpxQopaBqSbD7UbPYVVeR2bOSPYUm5/7zfnRoVqFLgelPMEg9D+NJ5Ug/gP4UhjcL6UhA9KUhh1Uj8KaTQAUUmaM0Af/Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F068M15KE15/img_4719.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F068M15KE15-ce124fe32b', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': 'bellcute', 'users': ['U04BSASSPTL'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:30:22,455 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1698548753.617479'}, 'replacement': {'ts': '1698548753.617479', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'พรุ่งนี้ (วันจันทร์ ) น้องรัน datascientist  กับ น้องเชง Business Development team \nจะมาถ่ายรูปรับปริญญาที่ออฟฟิศเรานะคะ\n\nเชิญพี่ๆ น้องๆ เพื่อนๆ มาร่วมเฟรมยินดีกับน้องๆดัวยจ้า ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'u+FKj', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'พรุ่งนี้ (วันจันทร์ ) น้องรัน datascientist  กับ น้องเชง Business Development team \nจะมาถ่ายรูปรับปริญญาที่ออฟฟิศเรานะคะ\n\nเชิญพี่ๆ น้องๆ เพื่อนๆ มาร่วมเฟรมยินดีกับน้องๆดัวยจ้า '}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': '+1', 'users': ['U04BC08FZSP', 'U04BUQD5KS8', 'U051174HC0N'], 'count': 3}, {'name': 'mortar_board', 'users': ['U04BUQD5KS8', 'U04BKNM9HCN'], 'count': 2}, {'name': 'sparkles', 'users': ['U04BUQD5KS8', 'U04BKNM9HCN'], 'count': 2}, {'name': 'kancute', 'users': ['U04PVE9RG0G'], 'count': 1}, {'name': 'saluting_face', 'users': ['U04BKNM9HCN'], 'count': 1}, {'name': '+1::skin-tone-2', 'users': ['U04BS7QUBQT'], 'count': 1}], 'edited': {'user': 'U04CG20VDG8', 'ts': '1698548779.000000'}, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:30:34,613 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1674188293.485019'}, 'replacement': {'ts': '1674188293.485019', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'Room : 10D naka\n\nMonthly Product Feedback &amp; Roadmap after Go Live\nFriday, January 20 · 11:30am – 12:00pm\nGoogle Meet joining info\nVideo call link: <https://meet.google.com/jvo-eiun-nyc>\nOr dial: \u202a(TH) +66 2 844 9331\u202c PIN: \u202a497 ************\u202c#\nMore phone numbers: <https://tel.meet/jvo-eiun-nyc?pin=4979160807354>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'oN9', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Room : 10D naka\n\nMonthly Product Feedback & Roadmap after Go Live\nFriday, January 20 · 11:30am – 12:00pm\nGoogle Meet joining info\nVideo call link: '}, {'type': 'link', 'url': 'https://meet.google.com/jvo-eiun-nyc'}, {'type': 'text', 'text': '\nOr dial: \u202a(TH) +66 2 844 9331\u202c PIN: \u202a497 ************\u202c#\nMore phone numbers: '}, {'type': 'link', 'url': 'https://tel.meet/jvo-eiun-nyc?pin=4979160807354'}]}]}], 'attachments': [{'from_url': 'https://meet.google.com/jvo-eiun-nyc', 'thumb_url': 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-96dp/logo_meet_2020q4_color_2x_web_96dp.png', 'thumb_width': 192, 'thumb_height': 192, 'service_icon': 'http://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-24dp/logo_meet_2020q4_color_1x_web_24dp.png', 'id': 1, 'original_url': 'https://meet.google.com/jvo-eiun-nyc', 'fallback': 'Meet', 'text': 'Real-time meetings by Google. Using your browser, share your video, desktop, and presentations with teammates and customers.', 'title': 'Meet', 'title_link': 'https://meet.google.com/jvo-eiun-nyc', 'service_name': 'meet.google.com'}, {'from_url': 'https://tel.meet/jvo-eiun-nyc?pin=4979160807354', 'thumb_url': 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-96dp/logo_meet_2020q4_color_2x_web_96dp.png', 'thumb_width': 192, 'thumb_height': 192, 'service_icon': 'http://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-24dp/logo_meet_2020q4_color_1x_web_24dp.png', 'id': 2, 'original_url': 'https://tel.meet/jvo-eiun-nyc?pin=4979160807354', 'fallback': 'Meet', 'text': 'Real-time meetings by Google. Using your browser, share your video, desktop, and presentations with teammates and customers.', 'title': 'Meet', 'title_link': 'https://tel.meet/jvo-eiun-nyc?pin=4979160807354', 'service_name': 'meet.google.com'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:30:34,615 - fetch_conversations - INFO - Stored 120 messages for general
2025-05-26 08:30:34,615 - fetch_conversations - INFO - Processing conversation 2/164 (1.2%): random
2025-05-26 08:31:15,554 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1745991422.152259'}, 'replacement': {'ts': '1745991422.152259', 'type': 'message', 'user': 'U072NP1567R', 'text': 'Floor 10 lounge is open', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'lytYd', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Floor 10 lounge is open'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:31:53,250 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1734317778.733239'}, 'replacement': {'ts': '1734317778.733239', 'type': 'message', 'user': 'U0818LC97MZ', 'text': 'ขอบคุณค้าบ ซินเจียยู่อี่ซินนี้ฮวดไช้คับ :sparkles: :tada: :red_envelope: \U0001faad :confetti_ball: ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'PiYFz', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ขอบคุณค้าบ ซินเจียยู่อี่ซินนี้ฮวดไช้คับ '}, {'type': 'emoji', 'name': 'sparkles', 'unicode': '2728'}, {'type': 'text', 'text': ' '}, {'type': 'emoji', 'name': 'tada', 'unicode': '1f389'}, {'type': 'text', 'text': ' '}, {'type': 'emoji', 'name': 'red_envelope', 'unicode': '1f9e7'}, {'type': 'text', 'text': ' \U0001faad '}, {'type': 'emoji', 'name': 'confetti_ball', 'unicode': '1f38a'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [{'id': 'F0850VC9RNJ', 'created': 1734317687, 'timestamp': 1734317687, 'name': 'IMG_5684.jpg', 'title': 'IMG_5684', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U0818LC97MZ', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 998054, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F0850VC9RNJ/img_5684.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F0850VC9RNJ/download/img_5684.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F0850VC9RNJ-a4f5a49163/img_5684_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 2665, 'original_h': 3553, 'thumb_tiny': 'AwAwACStjNJ2zRnjFOjTcc4yBSbNoxTVxvSnBsU8xc/4VFjJouVyRZIHo3VEDjNLmmc7JZ1Qbdi7fXnNFv8Adb60ih5QABk5pG3W/owPXFI2i7RRYFVSR2p6zlugx9aiIIODQWpJsdMqowAzyM81HkUt02ZBj0qHNM5jTgPltwCc8ZHamvAZJTvYAdqfkhTtIDDkVVxK77iM1LKUrEht4IjhnYmoxAWG5WyDTxAzffxj0FTKAgwAaEIz5wVkwewqOrM6O8zERsR64qPypP8Ank35VYj/2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F0850VC9RNJ/img_5684.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F0850VC9RNJ-e665946796', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:32:14,186 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1733288831.388979'}, 'replacement': {'ts': '1733288831.388979', 'type': 'message', 'user': 'U07NWV886Q1', 'text': 'มาอีกแว้ววว ขอบคุณน้าาา :kissing_heart: :kissing_heart: ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'Oc4oX', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'มาอีกแว้ววว ขอบคุณน้าาา '}, {'type': 'emoji', 'name': 'kissing_heart', 'unicode': '1f618'}, {'type': 'text', 'text': ' '}, {'type': 'emoji', 'name': 'kissing_heart', 'unicode': '1f618'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [{'id': 'F083CLKSE3Y', 'created': 1733288800, 'timestamp': 1733288800, 'name': 'IMG_1829.jpg', 'title': 'IMG_1829', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U07NWV886Q1', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 1131122, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F083CLKSE3Y/img_1829.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F083CLKSE3Y/download/img_1829.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F083CLKSE3Y-1ce051ab28/img_1829_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 3024, 'original_h': 4032, 'thumb_tiny': 'AwAwACSsGFBOcfWofNapoxvQNznOMAf1oAcKcDxTwmR91sUNHgcHn0oAgdcsTu603Z/tVZMDnkoOfcUnkP8A3F/MUwK0ERMuGUjAJwRVyIMFJYHnpT13Djvjn2oiJaRSehNZyfQpLqKEcAsU4601wdo4q0eVb6VXi/eFgQcU7WYbopyPOHwGYDtim+Zcf3nq46MrYPPvim4Pof8AvmnqLQkZdgIzyT1pnzF+D070srgAsegqGE5yeaVrlXJ/3nd807ds59KTNNcnHAz7UCJ2lIPCGk85v7lMW545A4/Cl+0j0H507kn/2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F083CLKSE3Y/img_1829.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F083CLKSE3Y-78e8deec20', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:32:40,994 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1710594104.855089'}, 'replacement': {'ts': '1710594104.855089', 'type': 'message', 'user': 'U04CG20TQBS', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F06PYJ0LTAQ', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105206_973.MP4', 'title': '20240316_105206_973', 'mimetype': 'video/mp4', 'filetype': 'mp4', 'pretty_type': 'MPEG-4 video', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 796882, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'transcription': {'status': 'none'}, 'mp4': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PYJ0LTAQ-9e96941297/20240316_105206_973.mp4', 'url_private': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PYJ0LTAQ-9e96941297/20240316_105206_973.mp4', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PYJ0LTAQ/download/20240316_105206_973.mp4', 'hls': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PYJ0LTAQ-9e96941297/file.m3u8?_xcb=adfb8', 'hls_embed': 'data:application/vnd.apple.mpegurl;base64,I0VYVE0zVQojRVhULVgtVkVSU0lPTjozCiNFWFQtWC1JTkRFUEVOREVOVC1TRUdNRU5UUwojRVhULVgtU1RSRUFNLUlORjpCQU5EV0lEVEg9ODI3ODc2LEFWRVJBR0UtQkFORFdJRFRIPTgyNzg3NixDT0RFQ1M9ImF2YzEuNjQwMDFmLG1wNGEuNDAuNSIsUkVTT0xVVElPTj0xMDgweDcyMCxGUkFNRS1SQVRFPTI5Ljk3MApkYXRhOmFwcGxpY2F0aW9uL3ZuZC5hcHBsZS5tcGVndXJsO2Jhc2U2NCxJMFZZVkUwelZRb2pSVmhVTFZndFZrVlNVMGxQVGpvekNpTkZXRlF0V0MxVVFWSkhSVlJFVlZKQlZFbFBUam8zQ2lORldGUXRXQzFOUlVSSlFTMVRSVkZWUlU1RFJUb3hDaU5GV0ZRdFdDMVFURUZaVEVsVFZDMVVXVkJGT2xaUFJBb2pSVmhVU1U1R09qWXVNREEyTEFwb2RIUndjem92TDJacGJHVnpMbk5zWVdOckxtTnZiUzltYVd4bGN5MTBiV0l2VkRBMFFrTXdNbEZDU2xBdFJqQTJVRmxLTUV4VVFWRXRPV1U1TmprME1USTVOeTltYVd4bFgwaGZNalkwWHpFeU9EQjROekl3WHpNMU1EQkxRbEJUWHpkUlZrSlNYekF3TURBeExuUnpDaU5GV0ZSSlRrWTZNQzQxTURBc0NtaDBkSEJ6T2k4dlptbHNaWE11YzJ4aFkyc3VZMjl0TDJacGJHVnpMWFJ0WWk5VU1EUkNRekF5VVVKS1VDMUdNRFpRV1Vvd1RGUkJVUzA1WlRrMk9UUXhNamszTDJacGJHVmZTRjh5TmpSZk1USTRNSGczTWpCZk16VXdNRXRDVUZOZk4xRldRbEpmTURBd01ESXVkSE1LSTBWWVZDMVlMVVZPUkV4SlUxUUsK', 'mp4_low': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PYJ0LTAQ-9e96941297/20240316_105206_973_trans.mp4', 'duration_ms': 6506, 'media_display_type': 'video', 'thumb_video': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PYJ0LTAQ-9e96941297/20240316_105206_973_thumb_video.jpeg', 'thumb_video_w': 720, 'thumb_video_h': 480, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PYJ0LTAQ/20240316_105206_973.mp4', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PYJ0LTAQ-8074f63243', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06PW4LJ45Q', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105206_973.jpg', 'title': '20240316_105206_973', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 318652, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW4LJ45Q/20240316_105206_973.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW4LJ45Q/download/20240316_105206_973.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_360.jpg', 'thumb_360_w': 240, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_480.jpg', 'thumb_480_w': 320, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_720.jpg', 'thumb_720_w': 480, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1200, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_960.jpg', 'thumb_960_w': 640, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW4LJ45Q-0c9662ba6e/20240316_105206_973_1024.jpg', 'thumb_1024_w': 683, 'thumb_1024_h': 1024, 'original_w': 1200, 'original_h': 1800, 'thumb_tiny': 'AwAwACDQbPagEGommUdzn0xQJF4wck9sUBYkbPalBBqIzJj736Uu9Rj5s5oCxWIJYfIcDnGP89aPmyoCkYbI4/z60/dJu2gnj9PrR5jZUMw5JGKQxhGWHysQOeR/nrRhsqApBBznB59v1p++TdtBPH+eaPNbK7mGCSMCgAxGTkMx68DtQI4uDuP0x6VGctIpwSBmnYY9sHOeaB2HYjLZDMevTtQI4sj5jn0x6VHgtKOM4Hf6+tKQ20HkHPPHWgLH/9k=', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PW4LJ45Q/20240316_105206_973.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PW4LJ45Q-53a9353c7a', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06PW19E3K5', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105159_589_img_0081_20240316_105158_3600.jpg', 'title': '20240316_105159_589_img_0081_20240316_105158_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 617294, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW19E3K5/20240316_105159_589_img_0081_20240316_105158_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW19E3K5/download/20240316_105159_589_img_0081_20240316_105158_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E3K5-3c411d7a3f/20240316_105159_589_img_0081_20240316_105158_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC8fvN9aQsFBJIAHX2qC4tw8+8swzgDFRszxgu4G0HBz1PvSHYsrOjLlTnnHFPVg3Q1VVkZMnEbHBG49aGxG67R85OAT70AWsjOM8+lOj+7+JqnJkuN3KgnOOO1SrcRxKqsTgjIPWgLFOOR7ibdI2FHzHBwAKWRmlGwKdr/AHTjGTQlu4tuBkswLAenpRduTsVF4HQ4wPwoAljMbPhgGJG3OKTchcBoyEU4VvepAm1owq4SPOahRmVZIihPBwfagY65GItynawbPBqtJKXIZhgdsdqt7cIhcE+oPrVadl3FVXjPX3oQmf/Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PW19E3K5/20240316_105159_589_img_0081_20240316_105158_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PW19E3K5-3ee8efd7e5', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06PPFA824E', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105142_664_img_0080_20240316_105142_3600.jpg', 'title': '20240316_105142_664_img_0080_20240316_105142_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 598252, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PPFA824E/20240316_105142_664_img_0080_20240316_105142_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PPFA824E/download/20240316_105142_664_img_0080_20240316_105142_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PPFA824E-e8b4503fca/20240316_105142_664_img_0080_20240316_105142_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC6zKrHcwGT3NBdVBJYYHU56VHNGjOWYKx9KrMzwxA4GD8oU9fakOxbWdHPynPuKk3DGQQRVMqoyXbBPZe9IWUSptJUPycH8qALe9fX/wCtT4yCuQQRk9Koyhl2lsFS3zGrEDxqxAKru5xnFAWKdlFvJkP3UH609W81GVhhW4RiOKkMZFoYYTk455/OobiQoqKq4KnPTpQwRYSMRuzEhjn1yRTJHjWYcD5DyQOATQNsccIXJP8APNRKy/ZJODkk5+tAx91IrpH1yTkgelQTvvfevOQPwqZR5b7ZFBBXINV5IiP3m3ardMGhCZ//2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PPFA824E/20240316_105142_664_img_0080_20240316_105142_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PPFA824E-52ad5c7c66', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06PW19E675', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105125_720_img_0079_20240316_105125_3600.jpg', 'title': '20240316_105125_720_img_0079_20240316_105125_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 592810, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW19E675/20240316_105125_720_img_0079_20240316_105125_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PW19E675/download/20240316_105125_720_img_0079_20240316_105125_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PW19E675-94ab055d7e/20240316_105125_720_img_0079_20240316_105125_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC8SAzZIHNGcVVuDE0gMnOOgzTXDLEDuOFPA68Uh2LTSBeOp9BTwcjNZ+FGDsKswyAvUj3qSOT95tQ7ARnBHegLFssoJBI496dEQyZBBGT0qjIxVkLgFCx/Op4ZYo2KZC55oCxTsYvMcu/3U5OfWpGJdiu07HB2E+tAwbZobb5zj5iKS5ciNAqYCnqRwOOlDAkjG2b/AFisQoXH/wBekLqZhK6ZQHbu96RtsSwIoIJbdTWJNqq7MEkLyKBj7s71VEGcHP0qpLIXkLYwfarOXjn2nuvbvUcsTOhkyNwzkYouKx//2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PW19E675/20240316_105125_720_img_0079_20240316_105125_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PW19E675-fff954c342', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06QJTR7YL8', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105107_513_img_0078_20240316_105106_3600.jpg', 'title': '20240316_105107_513_img_0078_20240316_105106_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 536208, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06QJTR7YL8/20240316_105107_513_img_0078_20240316_105106_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06QJTR7YL8/download/20240316_105107_513_img_0078_20240316_105106_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06QJTR7YL8-3a0496f125/20240316_105107_513_img_0078_20240316_105106_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC8fvN9aKRsmN26Aj8elUWYxhdiMoIyATSGXmcKM9e3FCuGGQfwqkoKOyrGe2dpyKfIvlbccMxxu9jQFiy0iqOWHHWnxEMmQcgk81SU4Y7sFUO0E89asWzxjKKQOc4JoBorWpleNyXOxR0zSqVulOCAwI5Panyj7Np5T+IjB+pqnEdlrIwHJOM0wLYcRq7q5cZyBjH5U5ZWLFJlGduRjmobhwGhjjXDDkegqUo0swLEqAvb1pDI5UjlkCKQu3npVRiY5NoIOD1FW/K/e/7pxT2gUx7GAB6g+lAWuf/Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06QJTR7YL8/20240316_105107_513_img_0078_20240316_105106_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06QJTR7YL8-ebfe4f35f3', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06PFHFJSPR', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105049_316_img_0077_20240316_105048_3600.jpg', 'title': '20240316_105049_316_img_0077_20240316_105048_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 543498, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PFHFJSPR/20240316_105049_316_img_0077_20240316_105048_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06PFHFJSPR/download/20240316_105049_316_img_0077_20240316_105048_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06PFHFJSPR-c383017d0f/20240316_105049_316_img_0077_20240316_105048_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC9/E31pAwIyDxVZtk8pc5ZMYAPT61FvcRbPlbc2z6+9Idi4ZkBwGBb0Bp4cFQ2cA+vFVvKjjkxt4wWDelQFjcYVs9flFAWNAsAQCRk9Ki+2RRyeWSc55I6Cqk0zAbVB2j5dx9qkWRXyzKDxuPHSgLBdqILaJR97pmonj2WkcgzuLbvYUtzIt1cRRxnI7mrwACkY4HahgVFcyXJGQ0ewDA96sMiR7XjQA9OKXbjBCgZPOKiuJRGgDLkZ9aAK5jd5WXBK5Jp0qiK3Kr1JCtj2pXLsqvGe/Qd6l2Lhi4BVsE5ouOx/9k=', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06PFHFJSPR/20240316_105049_316_img_0077_20240316_105048_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06PFHFJSPR-7873d1ef4a', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F06Q8PVTUFK', 'created': 1710594091, 'timestamp': 1710594091, 'name': '20240316_105035_105_img_0076_20240316_105034_3600.jpg', 'title': '20240316_105035_105_img_0076_20240316_105034_3600', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04CG20TQBS', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 536905, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06Q8PVTUFK/20240316_105035_105_img_0076_20240316_105034_3600.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06Q8PVTUFK/download/20240316_105035_105_img_0076_20240316_105034_3600.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_360.jpg', 'thumb_360_w': 360, 'thumb_360_h': 240, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_480.jpg', 'thumb_480_w': 480, 'thumb_480_h': 320, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_720.jpg', 'thumb_720_w': 720, 'thumb_720_h': 480, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 533, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_960.jpg', 'thumb_960_w': 960, 'thumb_960_h': 640, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F06Q8PVTUFK-0f0662f8f5/20240316_105035_105_img_0076_20240316_105034_3600_1024.jpg', 'thumb_1024_w': 1024, 'thumb_1024_h': 683, 'original_w': 3600, 'original_h': 2400, 'thumb_tiny': 'AwAgADC3G5YEuNrZwR70/IHU1VZlZEaM4UvjJ6jiktz5k0hYllX5VBGeKQy0zhRnrngYoWRX6EZ9O9VY92Nqp8hbqP549KmWIBwQOVyQc0AS7lOcMOOvPSnRkFSQcjJqg/EyJIBtK8e9WrUKisiHIBz15FANFew2tEcgHDZ5qII0azhgw44I6VNYoUgU/wB/JqyQcjNAEECSR2wx97GQDTJpHCCOR/v/AMQGMe1XGAOM54OajZVk+VlBH06UXGZ8wRZvl+6pHFLbyN9pU5PLVZdVUAFQQzc/lVOUFZshCqnpmhCP/9k=', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06Q8PVTUFK/20240316_105035_105_img_0076_20240316_105034_3600.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06Q8PVTUFK-407fe4b8c7', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': '+1', 'users': ['U04BC08FZSP'], 'count': 1}, {'name': 'heart', 'users': ['U04BC08FZSP'], 'count': 1}, {'name': 'pray', 'users': ['U04BC08FZSP'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:33:17,631 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1679980632.821029'}, 'replacement': {'ts': '1679980632.821029', 'type': 'message', 'user': 'U04BSASSPTL', 'text': ':8187-heartache: ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'I=hE', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': '8187-heartache'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:33:33,381 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1676956058.834939'}, 'replacement': {'ts': '1676956058.834939', 'type': 'message', 'user': 'U04BSATCAM8', 'text': 'อันเชิญทีมส้มตำ ที่ร้านลูกหินเพชรบุรีซอย 7 ค่า', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 's39', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'อันเชิญทีมส้มตำ ที่ร้านลูกหินเพชรบุรีซอย 7 ค่า'}]}]}], 'attachments': [], 'files': [{'id': 'F04QGUXRXMG', 'created': 1676956045, 'timestamp': 1676956045, 'name': 'IMG_2405.jpg', 'title': 'IMG_2405', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04BSATCAM8', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 1114652, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'subtype': 'slack_image', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04QGUXRXMG/img_2405.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04QGUXRXMG/download/img_2405.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04QGUXRXMG-ca0f1f6a89/img_2405_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 3024, 'original_h': 4032, 'thumb_tiny': 'AwAwACS6c44xmkHIwcZ74pDhlIPQ0oCjkDmkA1iPM2+1GACB60AozsRgleDTgQwDCmBA7hWINJ5i/wCTUpRSclRRsT+6KAIXk2jjrQJCIScnOCaR8Kd49MYPrURJGME+tTcqwkN2q8EcdzUkdwGkZlxjAHWoGhZztTHPY1HnyGMbLzxmmJWvqSz+Y8md4x7HGKj2yf3/APx6gvuOQwx70mf9pfzoK90laXc3HSnLyM1DUisojIIOe1IC1EimMN3qvehGYk53DHQdBT4bhVQKcjHtRgyEtjrTJM7PoaM1dMIzwAPwo8ke35U7hY//2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04QGUXRXMG/img_2405.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04QGUXRXMG-2b0be5924b', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:33:53,233 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1674630895.309129'}, 'replacement': {'ts': '1674630895.309129', 'type': 'message', 'user': 'U04BEGQ8U5S', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F04LT99PKJM', 'created': 1674630891, 'timestamp': 1674630891, 'name': 'image.png', 'title': 'image.png', 'mimetype': 'image/png', 'filetype': 'png', 'pretty_type': 'PNG', 'user': 'U04BEGQ8U5S', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 527546, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04LT99PKJM/image.png', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04LT99PKJM/download/image.png', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LT99PKJM-95b58ef1c7/image_64.png', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LT99PKJM-95b58ef1c7/image_80.png', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LT99PKJM-95b58ef1c7/image_360.png', 'thumb_360_w': 306, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LT99PKJM-95b58ef1c7/image_480.png', 'thumb_480_w': 408, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LT99PKJM-95b58ef1c7/image_160.png', 'original_w': 611, 'original_h': 719, 'thumb_tiny': 'AwAwACiFZGBOMYNDTtjb0J701SO9LAiyT5Y/dPSqZKVxSJoxvIP1pDcuy9qtuJCHyBgDis7BSQg1KZbRKJ34GRil84+gqPcDRVED9vOKapxMhHrV+S0CncW+XHNRWtsFBlPJP3c9hSkUh00gVDlsY7YrOLbm3dzzV66j3LmqDDHpUoplxY0Me7ocU3avrUIYlRzS4rXmMbG00QbqzfnUOAke0dBwKit7mSSQJnI7kipnQlTzk+lZyNIjEUSRgVWkstzcEc+lWlXIGzjHY1GyqkhYHk9frStYdymICvDMMil8r3pbhCr7weG/Sodx9TVog//Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04LT99PKJM/image.png', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04LT99PKJM-ec22ac674e', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:34:08,925 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1670561191.712439'}, 'replacement': {'ts': '1670561191.712439', 'type': 'message', 'user': 'U04BBQ80F2B', 'text': 'รายการพี่แบงค์ไม่ร่วมรายการครับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'LeA', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'รายการพี่แบงค์ไม่ร่วมรายการครับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:34:23,812 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669959064.740769'}, 'replacement': {'ts': '1669959064.740769', 'type': 'message', 'user': 'U04BKNM3DQW', 'text': 'ขอบคุณบัดดี้นะคะ :tada:', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '8kX', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ขอบคุณบัดดี้นะคะ '}, {'type': 'emoji', 'name': 'tada', 'unicode': '1f389'}]}]}], 'attachments': [], 'files': [{'id': 'F04DF0JMFA7', 'created': 1669959046, 'timestamp': 1669959046, 'name': 'IMG_4073.jpg', 'title': 'IMG_4073', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04BKNM3DQW', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 1176299, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04DF0JMFA7/img_4073.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04DF0JMFA7/download/img_4073.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_360.jpg', 'thumb_360_w': 270, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_480.jpg', 'thumb_480_w': 360, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_720.jpg', 'thumb_720_w': 540, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1067, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_960.jpg', 'thumb_960_w': 720, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04DF0JMFA7-8b5b8561a0/img_4073_1024.jpg', 'thumb_1024_w': 768, 'thumb_1024_h': 1024, 'original_w': 3024, 'original_h': 4032, 'thumb_tiny': 'AwAwACRsCFZc8oOmD3pJUUbjIc57f4UruI5CPvEjPX9KimdZCpWNs45Pao6lCBk84IiZz0qVmiZQF42HjaOtEETFGYY6bfrSWsY35A4B4J6VVhFmF0jj2u2Dk4FSefD/AHxWdO5WTqBnniovNPrTQh8q45OC3WrXzPEjIByM1VO0ED9OtSoxWNRk47ZqXsXFczLEQYpjOKpZZ5GBZgM9QOKsBzxyaI5dnOCwxQtglHlKtyjLIBwcKOR3qHa3pVqRPNkLKSB05NN8hv7w/OmQPSPdJtCkYbBNTPD83zMAvamwFPmK9allCrHk81L1NYvlIXCDaqEk0xF2HnGPTNPj6hgOMZHHWmrGud5XI75PSmhTdxk8Do4wQQRkEGo/Lf1/WmM3zH9KTdVGZ//Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04DF0JMFA7/img_4073.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04DF0JMFA7-822d62f765', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:34:25,250 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669358040.618759'}, 'replacement': {'ts': '1669358040.618759', 'type': 'message', 'user': 'U04CG20S6RW', 'text': 'ขอบคุณบัดดี้นะคะ :face_holding_back_tears::heart_hands::skin-tone-2:\nวันนี้แนนซี่ sick leave แต่มีสายลับมาแจ้งข่าวค่ะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '=iyMW', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ขอบคุณบัดดี้นะคะ '}, {'type': 'emoji', 'name': 'face_holding_back_tears', 'unicode': '1f979'}, {'type': 'emoji', 'name': 'heart_hands', 'unicode': '1faf6-1f3fb', 'skin_tone': 2}, {'type': 'text', 'text': '\nวันนี้แนนซี่ sick leave แต่มีสายลับมาแจ้งข่าวค่ะ'}]}]}], 'attachments': [], 'files': [{'id': 'F04CFDVECF4', 'mode': 'tombstone'}], 'reactions': [{'name': 'smiling_face_with_3_hearts', 'users': ['U04BSAT9P9Q'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:34:25,251 - fetch_conversations - INFO - Stored 148 messages for random
2025-05-26 08:34:25,252 - fetch_conversations - INFO - Processing conversation 3/164 (1.8%): well-beat
2025-05-26 08:34:58,703 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1728289814.808419'}, 'replacement': {'ts': '1728289814.808419', 'type': 'message', 'user': 'U07PV66KCCC', 'text': '<@U07PV66KCCC> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:35:37,972 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1687748836.757589'}, 'replacement': {'ts': '1687748836.757589', 'type': 'message', 'user': 'U04BS7R6Z6F', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F05E580DCCS', 'created': 1687748802, 'timestamp': 1687748802, 'name': '[INTERNAL] Well Beat Presentation V.1.pptx', 'title': '[INTERNAL] Well Beat Presentation V.1.pptx', 'mimetype': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'filetype': 'pptx', 'pretty_type': 'PowerPoint presentation', 'user': 'U04BS7R6Z6F', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 14186148, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05E580DCCS/_internal__well_beat_presentation_v.1.pptx', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05E580DCCS/download/_internal__well_beat_presentation_v.1.pptx', 'media_display_type': 'unknown', 'converted_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05E580DCCS-b7c58ea9e7/_internal__well_beat_presentation_v.1_converted.pdf', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05E580DCCS-b7c58ea9e7/_internal__well_beat_presentation_v.1_thumb_pdf.png', 'thumb_pdf_w': 1467, 'thumb_pdf_h': 825, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05E580DCCS/_internal__well_beat_presentation_v.1.pptx', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05E580DCCS-d9ee04ec99', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': 'pray', 'users': ['U04RR644049'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:35:37,974 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669789906.352689'}, 'replacement': {'ts': '1669789906.352689', 'type': 'message', 'user': 'U04BS0WNJCW', 'text': '<@U04BS0WNJCW> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:35:37,974 - fetch_conversations - INFO - Stored 47 messages for well-beat
2025-05-26 08:35:37,974 - fetch_conversations - INFO - Processing conversation 4/164 (2.4%): innovation-team-รวม
2025-05-26 08:35:39,439 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1727173081.314599'}, 'replacement': {'ts': '1727173081.314599', 'type': 'message', 'user': 'U05QJTPSHR8', 'text': 'de Bruijn graphs (DBGs)', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'bjeIr', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'de Bruijn graphs (DBGs)'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:35:39,439 - fetch_conversations - INFO - Stored 0 messages for innovation-team-รวม
2025-05-26 08:35:39,439 - fetch_conversations - INFO - Processing conversation 5/164 (3.0%): 4-innovation-team
2025-05-26 08:36:17,316 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1730440230.113369'}, 'replacement': {'ts': '1730440230.113369', 'type': 'message', 'user': 'U07PV66KCCC', 'text': '<@U07PV66KCCC> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [{'name': 'heart_eyes', 'users': ['U07PV66KCCC'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:36:28,873 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1688117387.771539'}, 'replacement': {'ts': '1688117387.771539', 'type': 'message', 'user': 'U04BS7R6Z6F', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F05F0CGK5QA', 'created': 1688117355, 'timestamp': 1688117355, 'name': 'FM011-TIME SHEET June.xlsx', 'title': 'FM011-TIME SHEET June.xlsx', 'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'filetype': 'xlsx', 'pretty_type': 'Excel spreadsheet', 'user': 'U04BS7R6Z6F', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 42450, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05F0CGK5QA/fm011-time_sheet_june.xlsx', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05F0CGK5QA/download/fm011-time_sheet_june.xlsx', 'media_display_type': 'unknown', 'converted_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05F0CGK5QA-3b257d01d0/fm011-time_sheet_june_converted.pdf', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05F0CGK5QA-3b257d01d0/fm011-time_sheet_june_thumb_pdf.png', 'thumb_pdf_w': 909, 'thumb_pdf_h': 1286, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05F0CGK5QA/fm011-time_sheet_june.xlsx', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05F0CGK5QA-a5b6f69951', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': '+1', 'users': ['U051174HC0N'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:36:38,982 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1685787797.038309'}, 'replacement': {'ts': '1685787797.038309', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '<https://www.nature.com/articles/s41440-023-01317-8|https://www.nature.com/articles/s41440-023-01317-8>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'IG3', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www.nature.com/articles/s41440-023-01317-8', 'text': 'https://www.nature.com/articles/s41440-023-01317-8'}]}]}], 'attachments': [{'from_url': 'https://www.nature.com/articles/s41440-023-01317-8', 'image_url': 'https://media.springernature.com/full/springer-static/image/art%3A10.1038%2Fs41440-023-01317-8/MediaObjects/41440_2023_1317_Figa_HTML.png', 'image_width': 2007, 'image_height': 1162, 'image_bytes': 109925, 'service_icon': 'https://www.nature.com/static/images/favicons/nature/apple-touch-icon-f39cb19454.png', 'id': 1, 'original_url': 'https://www.nature.com/articles/s41440-023-01317-8', 'fallback': 'Nature: Digital health, digital medicine, and digital therapeutics in cardiology: current evidence and future perspective in Japan', 'text': 'Hypertension Research - Digital health, digital medicine, and digital therapeutics in cardiology: current evidence and future perspective in Japan', 'title': 'Digital health, digital medicine, and digital therapeutics in cardiology: current evidence and future perspective in Japan', 'title_link': 'https://www.nature.com/articles/s41440-023-01317-8', 'service_name': 'Nature'}], 'files': [], 'reactions': [{'name': '+1', 'users': ['U04BKNM9HCN'], 'count': 1}, {'name': 'star-struck', 'users': ['U055Q3FDWN9'], 'count': 1}, {'name': '100_rainbow', 'users': ['U055Q3FDWN9'], 'count': 1}, {'name': '+1::skin-tone-3', 'users': ['U04BS7R6Z6F'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:36:58,623 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.441229'}, 'replacement': {'ts': '**********.441229', 'type': 'message', 'user': 'U04BEGQ8U5S', 'text': '<https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html|https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'e8Tj', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html', 'text': 'https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html'}]}]}], 'attachments': [{'image_url': 'https://image.cnbcfm.com/api/v1/image/107200349-16775183662023-02-27t170818z_1823284669_rc2rjz9gd7u8_rtrmadp_0_tech-conference-mwc.jpeg?v=1684238401', 'image_width': 3500, 'image_height': 2376, 'image_bytes': 463247, 'from_url': 'https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html', 'service_icon': 'https://www-cnbc-com.cdn.ampproject.org/favicon.ico', 'id': 1, 'original_url': 'https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html', 'fallback': 'CNBC: Google Cloud launches A.I.-powered tools to accelerate drug discovery, precision medicine', 'text': 'The solutions mark Google’s latest advancements in the AI arms race and hope to address a long-standing issue facing biotech and pharmaceutical companies.', 'title': 'Google Cloud launches A.I.-powered tools to accelerate drug discovery, precision medicine', 'title_link': 'https://www-cnbc-com.cdn.ampproject.org/c/s/www.cnbc.com/amp/2023/05/16/google-cloud-launches-ai-tools-to-accelerate-drug-discovery.html', 'service_name': 'CNBC'}], 'files': [], 'reactions': [{'name': 'fast_parrot', 'users': ['U04JU2ETNH4'], 'count': 1}, {'name': 'pray::skin-tone-2', 'users': ['U04JU2ETNH4'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:14,019 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1682502041.041269'}, 'replacement': {'ts': '1682502041.041269', 'type': 'message', 'user': 'U051174HC0N', 'text': 'พี่โทนี่ครับ MMT มี Mobile checkup ไหมครับ <@U04BEGQ8U5S> ฝั่งประกันชีวิต จะส่งออกไปกรณีลูกค้า High premium แล้วส่งไปตรวจที่บ้านก่อนทำประกันครับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'Vb36a', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'พี่โทนี่ครับ MMT มี Mobile checkup ไหมครับ '}, {'type': 'user', 'user_id': 'U04BEGQ8U5S'}, {'type': 'text', 'text': ' ฝั่งประกันชีวิต จะส่งออกไปกรณีลูกค้า High premium แล้วส่งไปตรวจที่บ้านก่อนทำประกันครับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:27,321 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1679044968.424949'}, 'replacement': {'ts': '1679044968.424949', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '*เก่งมากกกกกก! ภูมิใจจังงง ~* ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'hKa6', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เก่งมากกกกกก! ภูมิใจจังงง ~ ', 'style': {'bold': True}}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:39,104 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.033779'}, 'replacement': {'ts': '**********.033779', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '1. “Deep learning for health informatics” by Rajkomar A, Dean J, Kohane I (published in Nature Biomedical Engineering, 2019)\n', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'PNdYO', 'elements': [{'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '“Deep learning for health informatics” by Rajkomar A, Dean J, Kohane I (published in Nature Biomedical Engineering, 2019)'}]}], 'style': 'ordered', 'indent': 0, 'border': 0}, {'type': 'rich_text_section', 'elements': []}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:43,673 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.339469'}, 'replacement': {'ts': '**********.339469', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '*So beautiful! Love working in the aesthetic environment~*\n*`Beauty is power! :D`*\nThank you guys <!channel> so much to help make this happen na! :sparkles:', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'meM', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'So beautiful! Love working in the aesthetic environment~', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}, {'type': 'text', 'text': 'Beauty is power! :D', 'style': {'bold': True, 'code': True}}, {'type': 'text', 'text': '\nThank you guys '}, {'type': 'broadcast', 'range': 'channel'}, {'type': 'text', 'text': ' so much to help make this happen na! '}, {'type': 'emoji', 'name': 'sparkles', 'unicode': '2728'}]}]}], 'attachments': [], 'files': [{'id': 'F04L68VS6ET', 'created': 1674706467, 'timestamp': 1674706467, 'name': 'Screenshot 2566-01-26 at 11.14.15.png', 'title': 'Screenshot 2566-01-26 at 11.14.15.png', 'mimetype': 'image/png', 'filetype': 'png', 'pretty_type': 'PNG', 'user': 'U04JU2ETNH4', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 634851, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04L68VS6ET/screenshot_2566-01-26_at_11.14.15.png', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04L68VS6ET/download/screenshot_2566-01-26_at_11.14.15.png', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_64.png', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_80.png', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_360.png', 'thumb_360_w': 360, 'thumb_360_h': 300, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_480.png', 'thumb_480_w': 480, 'thumb_480_h': 400, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_160.png', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_720.png', 'thumb_720_w': 720, 'thumb_720_h': 600, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_800.png', 'thumb_800_w': 800, 'thumb_800_h': 667, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_960.png', 'thumb_960_w': 960, 'thumb_960_h': 800, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04L68VS6ET-0268e1b04e/screenshot_2566-01-26_at_11.14.15_1024.png', 'thumb_1024_w': 1024, 'thumb_1024_h': 854, 'original_w': 1832, 'original_h': 1527, 'thumb_tiny': 'AwAoADCezt4mtoy0aEle6ipTbw5/1Sf98iksT/okX+7Ux60ARfZ4P+eUf/fIpfs0H/PJP++RUuBTEYszg/wmgBv2aH/nkn/fIqG7giW1kKxoCBwQoqxMxSJmXqBUNyS2nux6lM0W0uA6yH+iRf7tTHrUVl/x5xf7tTHrQACo41w8hznJ/KpajVss/HT9aTC4TDdCwzjjqahuRjT3Gc4TrU7/AOrORkY6VDdf8eMnb5ad9LAQ2t7bx2yI8mGAwRg1Kb+2/wCev6GsOigDcF/a4/1v6GmLe24aTMnXpwaxqKANt7+2KECXn6Gobi8ge1eNXyxX0NZVFAH/2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04L68VS6ET/screenshot_2566-01-26_at_11.14.15.png', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04L68VS6ET-30a3355635', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': '+1', 'users': ['U04BC08FZSP', 'U04BKNM9HCN'], 'count': 2}, {'name': 'tada', 'users': ['U04BUQD5KS8', 'U04BS7R6Z6F'], 'count': 2}, {'name': '+1::skin-tone-3', 'users': ['U04BS7R6Z6F'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:43,674 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669002241.772389'}, 'replacement': {'ts': '1669002241.772389', 'type': 'message', 'user': 'U04CG20TQBS', 'text': '<@U04CG20TQBS> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:37:43,674 - fetch_conversations - INFO - Stored 76 messages for 4-innovation-team
2025-05-26 08:37:43,674 - fetch_conversations - INFO - Processing conversation 6/164 (3.7%): squad_wellhealth
2025-05-26 08:39:19,550 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.845789'}, 'replacement': {'ts': '**********.845789', 'type': 'message', 'user': 'U04P87WDQN8', 'text': '<@U04RSN3783X> <@U04RY5AKRLL>\nพี่ตั้มครับ WellHealth รพ.เวียงป่าเป้า กับ Top Expert Nursing Home กด Export data ของคนไข้ ไม่ได้ครับ\nเจอ error 500 ตอนกด export\n\nแต่ของรพ.ผู้ใช้งานฟรี invitrace กด export ได้ปกติ\n\n<https://invitracehealth.atlassian.net/browse/WLH-159>\n\nCC: <@U07EM6A29CJ>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'jtkK+', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04RSN3783X'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04RY5AKRLL'}, {'type': 'text', 'text': '\nพี่ตั้มครับ WellHealth รพ.เวียงป่าเป้า กับ Top Expert Nursing Home กด Export data ของคนไข้ ไม่ได้ครับ\nเจอ error 500 ตอนกด export\n\nแต่ของรพ.ผู้ใช้งานฟรี invitrace กด export ได้ปกติ\n\n'}, {'type': 'link', 'url': 'https://invitracehealth.atlassian.net/browse/WLH-159'}, {'type': 'text', 'text': '\n\nCC: '}, {'type': 'user', 'user_id': 'U07EM6A29CJ'}]}]}], 'attachments': [{'id': 1, 'blocks': [{'type': 'section', 'block_id': 'uf:ih:19967::cbb1e9d8d6614342a4f77d276633e92b', 'text': {'type': 'mrkdwn', 'text': '*<https://invitracehealth.atlassian.net/browse/WLH-159?atlOrigin=eyJpIjoiN2FlZTJlNjEwMDkzNDQ0ZmFkYTU0ZWUyYmRlZGI0NmMiLCJwIjoiamlyYS1zbGFjay1pbnQifQ|WLH-159 [BO] Cannot export PDF FIle>*', 'verbatim': False}}, {'type': 'context', 'block_id': 'uf:cx:19967::9e7f7b6f75194f30b1000a8079edccf7', 'elements': [{'type': 'mrkdwn', 'text': 'Status: *To Do*', 'verbatim': False}, {'type': 'image', 'image_url': 'https://product-integrations-cdn.atl-paas.net/jira-issuetype/bug.png', 'alt_text': 'Bug'}, {'type': 'mrkdwn', 'text': 'Type: *Bug*', 'verbatim': False}, {'type': 'image', 'image_url': 'https://product-integrations-cdn.atl-paas.net/icons/unknown-user.png', 'alt_text': 'Unassigned'}, {'type': 'mrkdwn', 'text': 'Assignee: *Unassigned*', 'verbatim': False}, {'type': 'image', 'image_url': 'https://product-integrations-cdn.atl-paas.net/jira-priority/high.png', 'alt_text': 'High'}, {'type': 'mrkdwn', 'text': 'Priority: *High*', 'verbatim': False}]}, {'type': 'actions', 'block_id': 'issueFooterActions:{"issueId":"19967","instanceId":"da9cb863-9f2c-3d8e-ac42-5ac71df372be","projectId":"10298","issueKey":"WLH-159","messageType":"uf","nType":"ISSUE_UNFURL"}', 'elements': [{'type': 'button', 'action_id': 'assign', 'text': {'type': 'plain_text', 'text': 'Assign', 'emoji': True}, 'value': 'assign'}, {'type': 'button', 'action_id': 'transition', 'text': {'type': 'plain_text', 'text': 'Change status', 'emoji': True}, 'value': 'transition'}, {'type': 'button', 'action_id': 'comment', 'text': {'type': 'plain_text', 'text': 'Comment', 'emoji': True}, 'value': 'comment'}, {'type': 'static_select', 'action_id': 'jiraOverflowActions', 'placeholder': {'type': 'plain_text', 'text': 'More actions...', 'emoji': True}, 'options': [{'text': {'type': 'plain_text', 'text': 'Watch', 'emoji': True}, 'value': 'watch'}, {'text': {'type': 'plain_text', 'text': 'Why am I seeing this?', 'emoji': True}, 'value': 'why'}, {'text': {'type': 'plain_text', 'text': 'Receive updates in channel', 'emoji': True}, 'value': 'connect'}]}]}], 'color': '#2684FF', 'fallback': '[no preview available]', 'bot_id': 'B0751LY4ZLN', 'bot_team_id': 'T04BC02QBJP', 'app_unfurl_url': 'https://invitracehealth.atlassian.net/browse/WLH-159', 'is_app_unfurl': True, 'app_id': 'A2RPP3NFR'}], 'files': [{'id': 'F08LPGD3DU1', 'created': **********, 'timestamp': **********, 'name': 'Screenshot 2568-04-02 at 14.32.35.png', 'title': 'Screenshot 2568-04-02 at 14.32.35.png', 'mimetype': 'image/png', 'filetype': 'png', 'pretty_type': 'PNG', 'user': 'U04P87WDQN8', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 620136, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F08LPGD3DU1/screenshot_2568-04-02_at_14.32.35.png', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F08LPGD3DU1/download/screenshot_2568-04-02_at_14.32.35.png', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_64.png', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_80.png', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_360.png', 'thumb_360_w': 360, 'thumb_360_h': 179, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_480.png', 'thumb_480_w': 480, 'thumb_480_h': 238, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_160.png', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_720.png', 'thumb_720_w': 720, 'thumb_720_h': 357, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_800.png', 'thumb_800_w': 800, 'thumb_800_h': 397, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_960.png', 'thumb_960_w': 960, 'thumb_960_h': 476, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08LPGD3DU1-5caf2fd5b6/screenshot_2568-04-02_at_14.32.35_1024.png', 'thumb_1024_w': 1024, 'thumb_1024_h': 508, 'original_w': 2880, 'original_h': 1428, 'thumb_tiny': 'AwAXADCqZH7FiPpU6EmME9SKT+z5f+ekf51YS0kCKMrwPWgC4h/dL0ztFDnMTZxnaelJsfCgOVwMYABo2PtYFy2RgcAUAZ/bijn2qf7LJ6rR9lk9RQBa2D0H5UoGPalooAKKKKAFopKKAP/Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F08LPGD3DU1/screenshot_2568-04-02_at_14.32.35.png', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F08LPGD3DU1-b7c935fbaf', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:40:03,938 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1689133402.426349'}, 'replacement': {'ts': '1689133402.426349', 'type': 'message', 'user': 'U04HRH0TURH', 'text': 'ถ้าจะมี ก็อาจจะเป็น ตรง หน้า Monitor มั้ยนะ ที่มีรายชื่อ คนไข้  แต่จำไม่ได้ว่ามี pagination มั้ย', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'KNJb3', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ถ้าจะมี ก็อาจจะเป็น ตรง หน้า Monitor มั้ยนะ ที่มีรายชื่อ คนไข้  แต่จำไม่ได้ว่ามี pagination มั้ย'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:40:14,019 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1677576255.382319'}, 'replacement': {'ts': '1677576255.382319', 'type': 'message', 'user': 'U04BEGQ8U5S', 'text': 'ขอรหัสทีคับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'oYFq', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ขอรหัสทีคับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:40:21,563 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1672025481.616949'}, 'replacement': {'ts': '1672025481.616949', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'และจะใช้อันนี้คุยกับหมอโหม่งด้วย', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'u/6m', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'และจะใช้อันนี้คุยกับหมอโหม่งด้วย'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:40:21,563 - fetch_conversations - INFO - Stored 102 messages for squad_wellhealth
2025-05-26 08:40:21,563 - fetch_conversations - INFO - Processing conversation 7/164 (4.3%): well-screening-her-will
2025-05-26 08:40:39,868 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.871409'}, 'replacement': {'ts': '**********.871409', 'type': 'message', 'user': 'U04NR7SSFHD', 'text': 'เดี๋ยวหมีประสานกับลุกค้าทางอีเมลเพื่อขอใบ PO และ signed quotation ต่อนะครับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'NiKiX', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เดี๋ยวหมีประสานกับลุกค้าทางอีเมลเพื่อขอใบ PO และ signed quotation ต่อนะครับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': {'user': 'U04NR7SSFHD', 'ts': '1696931570.000000'}, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:40:39,869 - fetch_conversations - INFO - Stored 11 messages for well-screening-her-will
2025-05-26 08:40:39,869 - fetch_conversations - INFO - Processing conversation 8/164 (4.9%): 9-algorithm-addwise
2025-05-26 08:42:02,966 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1747715813.798599'}, 'replacement': {'ts': '1747715813.798599', 'type': 'message', 'user': 'U072NP1567R', 'text': '<@U072NP1567R> has left the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_leave', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:43:11,671 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1728274391.284849'}, 'replacement': {'ts': '1728274391.284849', 'type': 'message', 'user': 'U04RY5AKRLL', 'text': '<@U04RY5AKRLL> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:44:11,905 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1721380885.847549'}, 'replacement': {'ts': '1721380885.847549', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '10C', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '8U6Jv', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '10C'}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': 'saluting_face', 'users': ['U077A5MFK0T'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:44:45,620 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1712717988.329119'}, 'replacement': {'ts': '1712717988.329119', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'ADDWISE-Weekly progress update2024\nWednesday, April 10*⋅*11:00 – 11:30am\nWeekly on Wednesday, until Dec 25, 2024', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'jxn9c', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ADDWISE-Weekly progress update2024\nWednesday, April 10'}, {'type': 'text', 'text': '⋅', 'style': {'bold': True}}, {'type': 'text', 'text': '11:00 – 11:30am\nWeekly on Wednesday, until Dec 25, 2024'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:45:23,095 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1699428593.899099'}, 'replacement': {'ts': '1699428593.899099', 'type': 'message', 'user': 'U04BKNM9HCN', 'text': 'PTS.xlsx\nPLC.xlsx\nPLD.xlsx\nPLK.xlsx\nPLP.xlsx\nPLR.xlsx\nPLS.xlsx\nPT1.xlsx\nPT2.xlsx\nPT3.xlsx\nPTN.xlsx', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'Kv0U8', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'PTS.xlsx\nPLC.xlsx\nPLD.xlsx\nPLK.xlsx\nPLP.xlsx\nPLR.xlsx\nPLS.xlsx\nPT1.xlsx\nPT2.xlsx\nPT3.xlsx\nPTN.xlsx'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:46:41,074 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1691557887.801699'}, 'replacement': {'ts': '1691557887.801699', 'type': 'message', 'user': 'U0579MHFX8S', 'text': '<@U04HRH0TURH> คุณตั้มมี assign ใครมาช่วยลงรายละเอียด part ที่เหลือที่ต้อง นำไป uat พรุ่งนี้กับลูกค้า มั้ยคะ ช่วงบ่าย3 ทีมจะมีการทดสอบ scenario ทั้งหมดก่อนไปพบลูกค้าอีกรอบค่ะ ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '1eot3', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04HRH0TURH'}, {'type': 'text', 'text': ' คุณตั้มมี assign ใครมาช่วยลงรายละเอียด part ที่เหลือที่ต้อง นำไป uat พรุ่งนี้กับลูกค้า มั้ยคะ ช่วงบ่าย3 ทีมจะมีการทดสอบ scenario ทั้งหมดก่อนไปพบลูกค้าอีกรอบค่ะ '}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:46:51,140 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1678858924.501619'}, 'replacement': {'ts': '1678858924.501619', 'type': 'message', 'user': 'U04HRH0TURH', 'text': 'OK', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'ubx0', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'OK'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:47:01,579 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1672987531.771829'}, 'replacement': {'ts': '1672987531.771829', 'type': 'message', 'user': 'U04BSATCAM8', 'text': '<https://well-algorithm-dev.invitrace.app/system/home>\n<https://well-algorithm-dev.invitrace.app/system/risk-analysis>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'tXxw', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://well-algorithm-dev.invitrace.app/system/home'}, {'type': 'text', 'text': '\n'}, {'type': 'link', 'url': 'https://well-algorithm-dev.invitrace.app/system/risk-analysis'}]}]}], 'attachments': [{'from_url': 'https://well-algorithm-dev.invitrace.app/system/home', 'thumb_url': 'https://well-algorithm-dev.invitrace.app/android-chrome-192x192.png', 'thumb_width': 192, 'thumb_height': 192, 'service_icon': 'https://well-algorithm-dev.invitrace.app/android-chrome-192x192.png', 'id': 1, 'original_url': 'https://well-algorithm-dev.invitrace.app/system/home', 'fallback': 'Algorithm | Provider Portal', 'text': 'ระบบให้คำแนะนำการตัดสินใจทางคลินิก Clinical Decision Support Systems (CDSS) เพื่อช่วย\nสนับสนุนการตัดสินใจของบุคลากรทางการแพทย์', 'title': 'Algorithm | Provider Portal', 'title_link': 'https://well-algorithm-dev.invitrace.app/system/home', 'service_name': 'well-algorithm-dev.invitrace.app'}, {'from_url': 'https://well-algorithm-dev.invitrace.app/system/risk-analysis', 'thumb_url': 'https://well-algorithm-dev.invitrace.app/android-chrome-192x192.png', 'thumb_width': 192, 'thumb_height': 192, 'service_icon': 'https://well-algorithm-dev.invitrace.app/android-chrome-192x192.png', 'id': 2, 'original_url': 'https://well-algorithm-dev.invitrace.app/system/risk-analysis', 'fallback': 'Algorithm | Provider Portal', 'text': 'ระบบให้คำแนะนำการตัดสินใจทางคลินิก Clinical Decision Support Systems (CDSS) เพื่อช่วย\nสนับสนุนการตัดสินใจของบุคลากรทางการแพทย์', 'title': 'Algorithm | Provider Portal', 'title_link': 'https://well-algorithm-dev.invitrace.app/system/risk-analysis', 'service_name': 'well-algorithm-dev.invitrace.app'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:47:01,581 - fetch_conversations - INFO - Stored 252 messages for 9-algorithm-addwise
2025-05-26 08:47:01,581 - fetch_conversations - INFO - Processing conversation 9/164 (5.5%): get-health-project
2025-05-26 08:47:40,501 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.027949'}, 'replacement': {'ts': '**********.027949', 'type': 'message', 'user': 'U04LM9MDQP9', 'text': 'Get health 1.7 card for Dev <@U06RQT2AFFZ> <@U05H9LYFYSE>\n[GH1.7][App] Update user profile: <https://invitracehealth.atlassian.net/browse/GH-289>\n[GH1.7][App] Display Achievement Pop-up for Step Goal: <https://invitracehealth.atlassian.net/browse/GH-294>\n[GH1.7][App] เพิ่ม Connect Garmin  เข้า Get Health: <https://invitracehealth.atlassian.net/browse/GH-14>\n[GH1.7][App] เพิ่ม Connect Samsung  เข้า Get Health: <https://invitracehealth.atlassian.net/browse/GH-296>\n\nBRD: <https://docs.google.com/document/d/1gu3aREReez96l1TCGDEBLP3W_r4YD3I7/edit?usp=sharing&amp;ouid=113730270714069703593&amp;rtpof=true&amp;sd=true>\n\nCC: <@U04RNH07Y75> <@U07HENW8QKD> <@U07LDK7BDN1>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'qvhTu', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Get health 1.7 card for Dev '}, {'type': 'user', 'user_id': 'U06RQT2AFFZ'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U05H9LYFYSE'}, {'type': 'text', 'text': '\n[GH1.7][App] Update user profile: '}, {'type': 'link', 'url': 'https://invitracehealth.atlassian.net/browse/GH-289'}, {'type': 'text', 'text': '\n[GH1.7][App] Display Achievement Pop-up for Step Goal: '}, {'type': 'link', 'url': 'https://invitracehealth.atlassian.net/browse/GH-294'}, {'type': 'text', 'text': '\n[GH1.7][App] เพิ่ม Connect Garmin  เข้า Get Health: '}, {'type': 'link', 'url': 'https://invitracehealth.atlassian.net/browse/GH-14'}, {'type': 'text', 'text': '\n[GH1.7][App] เพิ่ม Connect Samsung  เข้า Get Health: '}, {'type': 'link', 'url': 'https://invitracehealth.atlassian.net/browse/GH-296'}, {'type': 'text', 'text': '\n\nBRD: '}, {'type': 'link', 'url': 'https://docs.google.com/document/d/1gu3aREReez96l1TCGDEBLP3W_r4YD3I7/edit?usp=sharing&ouid=113730270714069703593&rtpof=true&sd=true'}, {'type': 'text', 'text': '\n\nCC: '}, {'type': 'user', 'user_id': 'U04RNH07Y75'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U07HENW8QKD'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U07LDK7BDN1'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:08,542 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.000039'}, 'replacement': {'ts': '**********.000039', 'type': 'message', 'user': 'U04TN6W9G04', 'text': '<@U04BS7QUBQT> <@U05NP2NHQRE> <@U04LM9MDQP9> <@U056V5D299V> <@U04CG20VDG8>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '6qTZ', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04BS7QUBQT'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U05NP2NHQRE'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04LM9MDQP9'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U056V5D299V'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04CG20VDG8'}]}]}], 'attachments': [], 'files': [{'id': 'F05RPQLUSBT', 'created': **********, 'timestamp': **********, 'name': 'Get health - 07.09.23.pptx', 'title': 'Get health - 07.09.23.pptx', 'mimetype': 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'filetype': 'pptx', 'pretty_type': 'PowerPoint presentation', 'user': 'U04TN6W9G04', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 5623625, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05RPQLUSBT/get_health_-_07.09.23.pptx', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05RPQLUSBT/download/get_health_-_07.09.23.pptx', 'media_display_type': 'unknown', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05RPQLUSBT/get_health_-_07.09.23.pptx', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05RPQLUSBT-0359b1cdbe', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:15,611 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.259549'}, 'replacement': {'ts': '**********.259549', 'type': 'message', 'user': 'U04HRH0TURH', 'text': 'อ้าวอ่ะนะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'xift/', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'อ้าวอ่ะนะ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:15,613 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.676359'}, 'replacement': {'ts': '**********.676359', 'type': 'message', 'user': 'U04BC08FZSP', 'text': '<@U04BC08FZSP> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:15,614 - fetch_conversations - INFO - Stored 48 messages for get-health-project
2025-05-26 08:48:15,614 - fetch_conversations - INFO - Processing conversation 10/164 (6.1%): thonburi-med-int
2025-05-26 08:48:28,885 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.006639'}, 'replacement': {'ts': '**********.006639', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'ใช่ค่ะ หมด เมษา อีก 2 วัน\nจบ service', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'vZtGO', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ใช่ค่ะ หมด เมษา อีก 2 วัน\nจบ service'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:39,259 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1678863907.719969'}, 'replacement': {'ts': '1678863907.719969', 'type': 'message', 'user': 'U04BSATCAM8', 'text': 'TOR THG live (All phase)  (M1)\nWednesday, March 15 · 2:00 – 3:00pm\nGoogle Meet joining info\nVideo call link: <https://meet.google.com/iut-okir-fpm>\nOr dial: \u202a(TH) +66 2 844 9331\u202c PIN: \u202a807 ************\u202c\nMore phone numbers: <https://tel.meet/iut-okir-fpm?pin=8078095378877>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'ERp9', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'TOR THG live (All phase)  (M1)\nWednesday, March 15 · 2:00 – 3:00pm\nGoogle Meet joining info\nVideo call link: '}, {'type': 'link', 'url': 'https://meet.google.com/iut-okir-fpm'}, {'type': 'text', 'text': '\nOr dial: \u202a(TH) +66 2 844 9331\u202c PIN: \u202a807 ************\u202c\nMore phone numbers: '}, {'type': 'link', 'url': 'https://tel.meet/iut-okir-fpm?pin=8078095378877'}]}]}], 'attachments': [{'from_url': 'https://meet.google.com/iut-okir-fpm', 'service_icon': 'http://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-24dp/logo_meet_2020q4_color_1x_web_24dp.png', 'thumb_url': 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-96dp/logo_meet_2020q4_color_2x_web_96dp.png', 'thumb_width': 192, 'thumb_height': 192, 'id': 1, 'original_url': 'https://meet.google.com/iut-okir-fpm', 'fallback': 'Meet', 'text': 'Real-time meetings by Google. Using your browser, share your video, desktop, and presentations with teammates and customers.', 'title': 'Meet', 'title_link': 'https://meet.google.com/iut-okir-fpm', 'service_name': 'meet.google.com'}, {'from_url': 'https://tel.meet/iut-okir-fpm?pin=8078095378877', 'service_icon': 'http://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-24dp/logo_meet_2020q4_color_1x_web_24dp.png', 'thumb_url': 'https://fonts.gstatic.com/s/i/productlogos/meet_2020q4/v1/web-96dp/logo_meet_2020q4_color_2x_web_96dp.png', 'thumb_width': 192, 'thumb_height': 192, 'id': 2, 'original_url': 'https://tel.meet/iut-okir-fpm?pin=8078095378877', 'fallback': 'Meet', 'text': 'Real-time meetings by Google. Using your browser, share your video, desktop, and presentations with teammates and customers.', 'title': 'Meet', 'title_link': 'https://tel.meet/iut-okir-fpm?pin=8078095378877', 'service_name': 'meet.google.com'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:46,408 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1675311911.446829'}, 'replacement': {'ts': '1675311911.446829', 'type': 'message', 'user': 'U04HRH0TURH', 'text': 'tthg ตอนนี้เลือกไม่ได้', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'nbU', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'tthg ตอนนี้เลือกไม่ได้'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:50,722 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1674728669.068429'}, 'replacement': {'ts': '1674728669.068429', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F04LFBUC886', 'created': 1674728658, 'timestamp': 1674728658, 'name': 'THG_UX_Interface.pdf', 'title': 'THG_UX_Interface.pdf', 'mimetype': 'application/pdf', 'filetype': 'pdf', 'pretty_type': 'PDF', 'user': 'U04CG20VDG8', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 42078307, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04LFBUC886/thg_ux_interface.pdf', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04LFBUC886/download/thg_ux_interface.pdf', 'media_display_type': 'unknown', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04LFBUC886-e94b80e693/thg_ux_interface_thumb_pdf.png', 'thumb_pdf_w': 2933, 'thumb_pdf_h': 1650, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04LFBUC886/thg_ux_interface.pdf', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04LFBUC886-026f810564', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:50,723 - fetch_conversations - INFO - Stored 21 messages for thonburi-med-int
2025-05-26 08:48:50,723 - fetch_conversations - INFO - Processing conversation 11/164 (6.7%): invitrace-esports
2025-05-26 08:48:53,613 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1681307643.665509'}, 'replacement': {'ts': '1681307643.665509', 'type': 'message', 'user': 'U0514SCCYH3', 'text': '<@U0514SCCYH3> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:53,613 - fetch_conversations - INFO - Stored 1 messages for invitrace-esports
2025-05-26 08:48:53,613 - fetch_conversations - INFO - Processing conversation 12/164 (7.3%): param-9-hospital
2025-05-26 08:48:56,851 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1674375518.871409'}, 'replacement': {'ts': '1674375518.871409', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '<@U04JU2ETNH4> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:56,851 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669098289.729469'}, 'replacement': {'ts': '1669098289.729469', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'The skeleton for second pitch as we have meeting with Dr.Ohm will send you soon ka', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '1Z1H', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'The skeleton for second pitch as we have meeting with Dr.Ohm will send you soon ka'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:48:56,851 - fetch_conversations - INFO - Stored 1 messages for param-9-hospital
2025-05-26 08:48:56,851 - fetch_conversations - INFO - Processing conversation 13/164 (7.9%): i-live-well-project
2025-05-26 08:49:29,805 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.558439'}, 'replacement': {'ts': '**********.558439', 'type': 'message', 'user': 'U04LM9MDQP9', 'text': '<@U0511KJSS0K> <@U080HBHLZKN> <@U04RSN3783X> iLW x SEA GAMES รบกวนช่วย Est. ให้ป็อปทีค่ะ\nPresentation by pop: <https://invitracehealth-my.sharepoint.com/:p:/g/personal/pakwan_p_invitracehealth_onmicrosoft_com/EfljfhGwVLRLtnNeYTPaHmwBq4N71-lQX7poGGXNNS2Xag?e=whkGCX|https://invitracehealth-my.sharepoint.com/:p:/g/personal/pakwan_p_invitracehealth_onmicrosoft[…]jfhGwVLRLtnNeYTPaHmwBq4N71-lQX7poGGXNNS2Xag?e=whkGCX>\nEst. link ka : <https://docs.google.com/spreadsheets/d/12nqxttrjCGW3-qqo-3x5tlw_fZkSi4utwpRIik3-jx8/edit?usp=sharing>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'LbPEa', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U0511KJSS0K'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U080HBHLZKN'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04RSN3783X'}, {'type': 'text', 'text': ' iLW x SEA GAMES รบกวนช่วย Est. ให้ป็อปทีค่ะ\nPresentation by pop: '}, {'type': 'link', 'url': 'https://invitracehealth-my.sharepoint.com/:p:/g/personal/pakwan_p_invitracehealth_onmicrosoft_com/EfljfhGwVLRLtnNeYTPaHmwBq4N71-lQX7poGGXNNS2Xag?e=whkGCX', 'text': 'https://invitracehealth-my.sharepoint.com/:p:/g/personal/pakwan_p_invitracehealth_onmicrosoft[…]jfhGwVLRLtnNeYTPaHmwBq4N71-lQX7poGGXNNS2Xag?e=whkGCX'}, {'type': 'text', 'text': '\nEst. link ka : '}, {'type': 'link', 'url': 'https://docs.google.com/spreadsheets/d/12nqxttrjCGW3-qqo-3x5tlw_fZkSi4utwpRIik3-jx8/edit?usp=sharing'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': {'user': 'U04LM9MDQP9', 'ts': '**********.000000'}, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:49:55,327 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.724979'}, 'replacement': {'ts': '**********.724979', 'type': 'message', 'user': 'U04LM9MDQP9', 'text': '<@U04BPCFT3DK> <@U0511KJSS0K>\ni-LiveWell (IF and iTele adjust wording)\nขึ้น Production แล้ว ทั้ง PlayStore / AppStore', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'oig9N', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04BPCFT3DK'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U0511KJSS0K'}, {'type': 'text', 'text': '\ni-LiveWell (IF and iTele adjust wording)\nขึ้น Production แล้ว ทั้ง PlayStore / AppStore'}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': 'pray', 'users': ['U04BPCFT3DK'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:49:55,328 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1669696827.090769'}, 'replacement': {'ts': '1669696827.090769', 'type': 'message', 'user': 'U04BPCFT3DK', 'text': '<@U04BPCFT3DK> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:49:55,328 - fetch_conversations - INFO - Stored 37 messages for i-live-well-project
2025-05-26 08:49:55,328 - fetch_conversations - INFO - Processing conversation 14/164 (8.5%): ai-testing
2025-05-26 08:50:10,011 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1731295973.646439'}, 'replacement': {'ts': '1731295973.646439', 'type': 'message', 'user': 'U07F93Y4SCW', 'text': '<@U07F93Y4SCW> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:10,012 - fetch_conversations - INFO - Stored 8 messages for ai-testing
2025-05-26 08:50:10,012 - fetch_conversations - INFO - Processing conversation 15/164 (9.1%): phuonly
2025-05-26 08:50:11,450 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1671606753.808929'}, 'replacement': {'ts': '1671606753.808929', 'type': 'message', 'user': 'U04BBQ80F2B', 'text': '<https://to-do.microsoft.com/sharing?InvitationToken=zr7esEsTXLnWvKMjHmMocQ1RfjxOniZFuSis5nNIRitI5xETowPlM8jnLS2Ukw9q8|SLH Project Task>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'LxKR', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://to-do.microsoft.com/sharing?InvitationToken=zr7esEsTXLnWvKMjHmMocQ1RfjxOniZFuSis5nNIRitI5xETowPlM8jnLS2Ukw9q8', 'text': 'SLH Project Task'}]}]}], 'attachments': [{'from_url': 'https://to-do.microsoft.com/sharing?InvitationToken=zr7esEsTXLnWvKMjHmMocQ1RfjxOniZFuSis5nNIRitI5xETowPlM8jnLS2Ukw9q8', 'image_url': 'https://to-do-cdn.microsoft.com/webapp/38cce120a00363d6d88c924546055c873aae9c159c1b4d9ec724687af6112fd9/preview.png', 'image_width': 250, 'image_height': 250, 'image_bytes': 121344, 'service_icon': 'https://to-do-cdn.microsoft.com/webapp/e147f78a28f336cdc96bc4c4a86ad5bb19806a9c77983fc6b7db69e9d2933ee3/touch-icon-152x152.png', 'id': 1, 'original_url': 'https://to-do.microsoft.com/sharing?InvitationToken=zr7esEsTXLnWvKMjHmMocQ1RfjxOniZFuSis5nNIRitI5xETowPlM8jnLS2Ukw9q8', 'fallback': 'To Do: Microsoft To Do', 'text': 'Microsoft To Do', 'title': 'Microsoft To Do', 'title_link': 'https://to-do.microsoft.com/sharing?InvitationToken=zr7esEsTXLnWvKMjHmMocQ1RfjxOniZFuSis5nNIRitI5xETowPlM8jnLS2Ukw9q8', 'service_name': 'To Do'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:11,450 - fetch_conversations - INFO - Stored 0 messages for phuonly
2025-05-26 08:50:11,450 - fetch_conversations - INFO - Processing conversation 16/164 (9.8%): slh-product-design-team
2025-05-26 08:50:13,129 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1678872845.758649'}, 'replacement': {'ts': '1678872845.758649', 'type': 'message', 'user': 'U04EXRJ3BHQ', 'text': '<https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he8yQS461500239787:pl:ta:p1:p22,563,000:ac:ap:neg:fi:tikwd-65526620:lp1012728:li:dec:dm:ppccp=UmFuZG9tSVYkc2RlIyh9YYriJK-Ikd_dLBPOo0BdMww;ws=&amp;gclid=Cj0KCQjw2cWgBhDYARIsALggUhr9KA3YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB|https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he[…]YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'QcC', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he8yQS461500239787:pl:ta:p1:p22,563,000:ac:ap:neg:fi:tikwd-65526620:lp1012728:li:dec:dm:ppccp=UmFuZG9tSVYkc2RlIyh9YYriJK-Ikd_dLBPOo0BdMww;ws=&gclid=Cj0KCQjw2cWgBhDYARIsALggUhr9KA3YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB', 'text': 'https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he[…]YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB'}]}]}], 'attachments': [{'from_url': 'https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he8yQS461500239787:pl:ta:p1:p22,563,000:ac:ap:neg:fi:tikwd-65526620:lp1012728:li:dec:dm:ppccp=UmFuZG9tSVYkc2RlIyh9YYriJK-Ikd_dLBPOo0BdMww;ws=&gclid=Cj0KCQjw2cWgBhDYARIsALggUhr9KA3YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB', 'image_url': 'https://cf.bstatic.com/static/img/twitter-image-else/566c7081f1deeaca39957e96365c3908f83b95af.jpg', 'image_width': 1200, 'image_height': 900, 'image_bytes': 143735, 'service_icon': 'https://cf.bstatic.com/static/img/apple-touch-icon/c9b35bf29a75cac2f430f80a5d4bc7fd961d21a7.png', 'id': 1, 'original_url': 'https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he8yQS461500239787:pl:ta:p1:p22,563,000:ac:ap:neg:fi:tikwd-65526620:lp1012728:li:dec:dm:ppccp=UmFuZG9tSVYkc2RlIyh9YYriJK-Ikd_dLBPOo0BdMww;ws=&amp;gclid=Cj0KCQjw2cWgBhDYARIsALggUhr9KA3YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB', 'fallback': 'Booking.com: Booking.com: The largest selection of hotels, homes, and vacation rentals', 'text': 'Whether you’re looking for hotels, homes, or vacation rentals, you’ll always find the guaranteed best price. Browse our 2,563,380 accommodations in over 85,000 destinations.', 'title': 'Booking.com: The largest selection of hotels, homes, and vacation rentals', 'title_link': 'https://www.booking.com/index.html?aid=378266;label=booking-name-IquAp*EbiLS6jPVl_he8yQS461500239787:pl:ta:p1:p22,563,000:ac:ap:neg:fi:tikwd-65526620:lp1012728:li:dec:dm:ppccp=UmFuZG9tSVYkc2RlIyh9YYriJK-Ikd_dLBPOo0BdMww;ws=&gclid=Cj0KCQjw2cWgBhDYARIsALggUhr9KA3YAR24WHhK7vSRb7z4QuTJnHv4GIlkzjPrzAxSxipxWNHyQ28aAggnEALw_wcB', 'service_name': 'Booking.com'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:13,130 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1671611189.184949'}, 'replacement': {'ts': '1671611189.184949', 'type': 'message', 'user': 'U04BBQ80F2B', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F04G4U80NBC', 'created': 1671611182, 'timestamp': 1671611182, 'name': 'image.png', 'title': 'image.png', 'mimetype': 'image/png', 'filetype': 'png', 'pretty_type': 'PNG', 'user': 'U04BBQ80F2B', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 34482, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04G4U80NBC/image.png', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04G4U80NBC/download/image.png', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04G4U80NBC-b8c585e080/image_64.png', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04G4U80NBC-b8c585e080/image_80.png', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04G4U80NBC-b8c585e080/image_360.png', 'thumb_360_w': 318, 'thumb_360_h': 218, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04G4U80NBC-b8c585e080/image_160.png', 'original_w': 318, 'original_h': 218, 'thumb_tiny': 'AwAgADCC3tBNEXMm3Bxjbmoni2u67l+Ukc8U6KeWJCI3AGc4IHWmMWZixIJJyTQAhXAzlT9DTacVOeo/OkIx3FACUUUUAKCMHIyfWl3Ln7g6569vSkpKAFOOwxSUUUAFFKCPSgkHoMUAf//Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04G4U80NBC/image.png', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04G4U80NBC-42984c022f', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:13,130 - fetch_conversations - INFO - Stored 0 messages for slh-product-design-team
2025-05-26 08:50:13,130 - fetch_conversations - INFO - Processing conversation 17/164 (10.4%): share-cv-profile-for-recruit
2025-05-26 08:50:23,573 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1686016800.457399'}, 'replacement': {'ts': '1686016800.457399', 'type': 'message', 'user': 'U04BC08FZSP', 'text': '<@U04KD337GSK> รบกวนทางพี่พัช ทํา report สรุปกับทาง Head of department/manager ในส่วนการ recruitment ดังไฟล์แนบนี้ ส่งกลับให้ผม *(ภายใน 7 มิถุนายน)* \n• เนื้อหาหลักคือ 1) ต้องการกี่คนที่จะให้ sign เดือนนี้  2) ได้แล้วกี่คน 3) มีรอ interview แล้วกี่คน 4) หัวหน้า/lead ได้ทํางานร่วมกับฝ่ายบุคคลแล้วอย่างไรบ้าน ให้ตอบสั้นๆ 2 ข้อ \nพี่พัชรบกวนเปิดไฟล์แนบครับ และรบกวนทางหัวหน้าแผนกพูดคุยปรึกษากับ HR เพื่อหาแนวทาง\n\nรบกวนพี่พัชดําเนินเรื่องประสานงานหัวหน้าแผนกครับ และนําส่งไฟล์นี้ *(ภายใน 7 มิถุนายน) โดยขอให้หัวหน้าแผนกให้ความร่วมมือกับทาง HR ครับ*\n\n*ขอบคุณครับ*', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'J6M', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04KD337GSK'}, {'type': 'text', 'text': ' รบกวนทางพี่พัช ทํา report สรุปกับทาง Head of department/manager ในส่วนการ recruitment ดังไฟล์แนบนี้ ส่งกลับให้ผม '}, {'type': 'text', 'text': '(ภายใน 7 มิถุนายน) ', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}]}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เนื้อหาหลักคือ 1) ต้องการกี่คนที่จะให้ sign เดือนนี้  2) ได้แล้วกี่คน 3) มีรอ interview แล้วกี่คน 4) หัวหน้า/lead ได้ทํางานร่วมกับฝ่ายบุคคลแล้วอย่างไรบ้าน ให้ตอบสั้นๆ 2 ข้อ '}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '\nพี่พัชรบกวนเปิดไฟล์แนบครับ และรบกวนทางหัวหน้าแผนกพูดคุยปรึกษากับ HR เพื่อหาแนวทาง\n\nรบกวนพี่พัชดําเนินเรื่องประสานงานหัวหน้าแผนกครับ และนําส่งไฟล์นี้ '}, {'type': 'text', 'text': '(ภายใน 7 มิถุนายน) โดยขอให้หัวหน้าแผนกให้ความร่วมมือกับทาง HR ครับ', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'ขอบคุณครับ', 'style': {'bold': True}}]}]}], 'attachments': [], 'files': [{'id': 'F05B2GZFFGU', 'created': 1685975578, 'timestamp': 1685975578, 'name': 'กรอกข้อมูลลับภายใน 7 June 2023.xlsx', 'title': 'กรอกข้อมูลลับภายใน 7 June 2023.xlsx', 'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'filetype': 'xlsx', 'pretty_type': 'Excel spreadsheet', 'user': 'U04BC08FZSP', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 10807, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05B2GZFFGU/_______________________________________________________7_june_2023.xlsx', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05B2GZFFGU/download/_______________________________________________________7_june_2023.xlsx', 'media_display_type': 'unknown', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05B2GZFFGU/_______________________________________________________7_june_2023.xlsx', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05B2GZFFGU-d847a4e3ef', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:23,575 - fetch_conversations - INFO - Stored 6 messages for share-cv-profile-for-recruit
2025-05-26 08:50:23,575 - fetch_conversations - INFO - Processing conversation 18/164 (11.0%): product-lead-initiative
2025-05-26 08:50:26,646 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1704258979.536699'}, 'replacement': {'ts': '1704258979.536699', 'type': 'message', 'user': 'U06CLFXKNMP', 'text': '<@U06CLFXKNMP> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:26,646 - fetch_conversations - INFO - Stored 1 messages for product-lead-initiative
2025-05-26 08:50:26,646 - fetch_conversations - INFO - Processing conversation 19/164 (11.6%): consulting-g5
2025-05-26 08:50:37,598 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1744365281.496029'}, 'replacement': {'ts': '1744365281.496029', 'type': 'message', 'user': 'U08MH4RJ5LZ', 'text': '<@U08MH4RJ5LZ> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:49,151 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1701229617.920199'}, 'replacement': {'ts': '1701229617.920199', 'type': 'message', 'user': 'U04BC08FZSP', 'text': 'ในข้อ 2.', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'tvdrl', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ในข้อ 2.'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:50:54,846 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1692171778.899849'}, 'replacement': {'ts': '1692171778.899849', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '*G5  16/08/23*\n\n*Next Step :*\n\n*1. DTM*\n- พี่เนตรคุยกับคุณหมอในการ Pilot PYT2\n- พี่เบลล์ส่ง QT\n\n*2.Big Data Analysis*\n- Timeline + Example of Report\n- ทำ QT ได้เลย\n- Bend data and Invitrace คุยกันได้เลย\n\n*3. Sleep Test*\n- นัดคุยกับทีม Innovation\n\n*4. Telecare 4.0 Advance improvement*\n(ไม่ได้แตะมาหลายรอบ ที่ไปเจอ พหล)\n- เราอยากทำ Improvement ที่มีเรื่อง Pictures in Pictures\nจะไปจุดนั้นได้จะต้อง มี Task Function อะไรบ้าง\n-Dashboard Monitoring\n- E Certificated , Prescription\n\n*5. IPD*\n- ต้องมา Integrated with HIS ด้วย\n- Channel ต้องมี รับ medical devices ของทาง รพ ได้ด้วย\n- ประสานคุณวี เรื่องหา Site Pilot\n- การมีปัญหาเรื่องข้อมูลสุญหาย ต้องทำอย่างไร (ex: ระบบ Pacs) ในการแบ่งโหลด\n\nPs. Update พี่โทนี่ เรื่อง การใช้ tele in BDMS', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '7kAws', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'G5  16/08/23', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'Next Step :', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': '1. DTM', 'style': {'bold': True}}, {'type': 'text', 'text': '\n- พี่เนตรคุยกับคุณหมอในการ Pilot PYT2\n- พี่เบลล์ส่ง QT\n\n'}, {'type': 'text', 'text': '2.Big Data Analysis', 'style': {'bold': True}}, {'type': 'text', 'text': '\n- Timeline + Example of Report\n- ทำ QT ได้เลย\n- Bend data and Invitrace คุยกันได้เลย\n\n'}, {'type': 'text', 'text': '3. Sleep Test', 'style': {'bold': True}}, {'type': 'text', 'text': '\n- นัดคุยกับทีม Innovation\n\n'}, {'type': 'text', 'text': '4. Telecare 4.0 Advance improvement', 'style': {'bold': True}}, {'type': 'text', 'text': '\n(ไม่ได้แตะมาหลายรอบ ที่ไปเจอ พหล)\n- เราอยากทำ Improvement ที่มีเรื่อง Pictures in Pictures\nจะไปจุดนั้นได้จะต้อง มี Task Function อะไรบ้าง\n-Dashboard Monitoring\n- E Certificated , Prescription\n\n'}, {'type': 'text', 'text': '5. IPD', 'style': {'bold': True}}, {'type': 'text', 'text': '\n- ต้องมา Integrated with HIS ด้วย\n- Channel ต้องมี รับ medical devices ของทาง รพ ได้ด้วย\n- ประสานคุณวี เรื่องหา Site Pilot\n- การมีปัญหาเรื่องข้อมูลสุญหาย ต้องทำอย่างไร (ex: ระบบ Pacs) ในการแบ่งโหลด\n\nPs. Update พี่โทนี่ เรื่อง การใช้ tele in BDMS'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': {'user': 'U04CG20VDG8', 'ts': '**********.000000'}, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:00,946 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.127029'}, 'replacement': {'ts': '**********.127029', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F05AKU57QM7', 'created': 1685505628, 'timestamp': 1685505628, 'name': 'PDF.pdf', 'title': 'PDF.pdf', 'mimetype': 'application/pdf', 'filetype': 'pdf', 'pretty_type': 'PDF', 'user': 'U04JU2ETNH4', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 560388, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05AKU57QM7/pdf.pdf', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05AKU57QM7/download/pdf.pdf', 'media_display_type': 'unknown', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05AKU57QM7-606bb5668b/pdf_thumb_pdf.png', 'thumb_pdf_w': 909, 'thumb_pdf_h': 1286, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05AKU57QM7/pdf.pdf', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05AKU57QM7-ea845013dd', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:02,545 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1682429638.560569'}, 'replacement': {'ts': '1682429638.560569', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '<@U04BC08FZSP> <@U04JU2ETNH4>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '60Hft', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04BC08FZSP'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04JU2ETNH4'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:05,648 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1678248249.306839'}, 'replacement': {'ts': '1678248249.306839', 'type': 'message', 'user': 'U04BC08FZSP', 'text': 'เอาอุปกรณไป แต่ไม่ต้อง step by step แบบ pitch', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'TEd', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เอาอุปกรณไป แต่ไม่ต้อง step by step แบบ pitch'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:08,724 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1675389051.123909'}, 'replacement': {'ts': '1675389051.123909', 'type': 'message', 'user': 'U04JU2ETNH4', 'text': 'Ah~ Let me hand you in. I need to settle first.', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'z5p1x', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Ah~ Let me hand you in. I need to settle first.'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:08,725 - fetch_conversations - INFO - Stored 23 messages for consulting-g5
2025-05-26 08:51:08,725 - fetch_conversations - INFO - Processing conversation 20/164 (12.2%): product-management
2025-05-26 08:51:10,180 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1674120489.616749'}, 'replacement': {'ts': '1674120489.616749', 'type': 'message', 'user': 'U04BSASSPTL', 'text': '<https://webengage.com/blog/best-mobile-app-metrics/>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'BynrZ', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://webengage.com/blog/best-mobile-app-metrics/'}]}]}], 'attachments': [{'from_url': 'https://webengage.com/blog/best-mobile-app-metrics/', 'ts': 1573712649, 'image_url': 'https://content.webengage.com/wp-content/uploads/sites/4/2019/11/Hero-Image-App-Metrics.png', 'image_width': 667, 'image_height': 250, 'image_bytes': 33433, 'service_icon': 'https://content.webengage.com/wp-content/uploads/sites/4/2021/12/cropped-8-2-1-180x180.png', 'id': 1, 'original_url': 'https://webengage.com/blog/best-mobile-app-metrics/', 'fallback': 'WebEngage: The 16 Mobile App Metrics You Must Track In 2023 | WebEngage', 'text': 'Here are a few Mobile App Metrics you should track to monitor app growth, and optimize marketing campaigns to get higher user engagement!', 'title': 'The 16 Mobile App Metrics You Must Track In 2023 | WebEngage', 'title_link': 'https://webengage.com/blog/best-mobile-app-metrics/', 'service_name': 'WebEngage'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:10,182 - fetch_conversations - INFO - Stored 0 messages for product-management
2025-05-26 08:51:10,182 - fetch_conversations - INFO - Processing conversation 21/164 (12.8%): 5-head-of-operation
2025-05-26 08:51:23,955 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1680854386.821389'}, 'replacement': {'ts': '1680854386.821389', 'type': 'message', 'user': 'U04P12PUEUW', 'text': 'โพสต์แบบฟรี ตามที่พี่ <@U04BEGQ8U5S> แนะนำนะคับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '3PW+3', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'โพสต์แบบฟรี ตามที่พี่ '}, {'type': 'user', 'user_id': 'U04BEGQ8U5S'}, {'type': 'text', 'text': ' แนะนำนะคับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:23,956 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1677037168.317189'}, 'replacement': {'ts': '1677037168.317189', 'type': 'message', 'user': 'U04BC08FZSP', 'text': 'I just got a reminder that we have to submit this project situation report to CEO and board of director by tomorrow 10am krub.', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'S2P', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'I just got a reminder that we have to submit this project situation report to CEO and board of director by tomorrow 10am krub.'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:23,956 - fetch_conversations - INFO - Stored 8 messages for 5-head-of-operation
2025-05-26 08:51:23,956 - fetch_conversations - INFO - Processing conversation 22/164 (13.4%): announcement
2025-05-26 08:51:30,208 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.170959'}, 'replacement': {'ts': '**********.170959', 'type': 'message', 'user': None, 'text': 'AQI (ดัชนีคุณภาพอากาศ) : 91\nTime (ข้อมูล ณ เวลา) : 2025-02-25 08:00:00\n\nLevel:\nคุณภาพปานกลาง (Medium Quality, Moderate)\n\nDescription:\nไม่มีผลกระทบต่อสุขภาพ (No health effects)\n\nคำแนะนำ:\n[ประชาชนทั่วไป]\xa0สามารถทำกิจกรรมกลางแจ้งได้ตามปกติ\n[ประชาชนในกลุ่มเสี่ยง]\xa0หากมีอาการเบื้องต้น เช่น ไอ หายใจลำบาก ระคายเคือง ตา ควรลดระยะเวลาการทำกิจกรรมกลางแจ้ง', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'BTz3V', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'AQI (ดัชนีคุณภาพอากาศ) : 91\nTime (ข้อมูล ณ เวลา) : 2025-02-25 08:00:00\n\nLevel:\nคุณภาพปานกลาง (Medium Quality, Moderate)\n\nDescription:\nไม่มีผลกระทบต่อสุขภาพ (No health effects)\n\nคำแนะนำ:\n[ประชาชนทั่วไป]\xa0สามารถทำกิจกรรมกลางแจ้งได้ตามปกติ\n[ประชาชนในกลุ่มเสี่ยง]\xa0หากมีอาการเบื้องต้น เช่น ไอ หายใจลำบาก ระคายเคือง ตา ควรลดระยะเวลาการทำกิจกรรมกลางแจ้ง'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': 'B089TCDS8J0', 'subtype': 'bot_message', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:51:30,209 - fetch_conversations - INFO - Stored 3 messages for announcement
2025-05-26 08:51:30,209 - fetch_conversations - INFO - Processing conversation 23/164 (14.0%): back-offices-archived-archived
2025-05-26 08:51:48,856 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1747795680.877299'}, 'replacement': {'ts': '1747795680.877299', 'type': 'message', 'user': 'U08TZLJF724', 'text': '<@U08TZLJF724> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:52:18,993 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1735015867.546059'}, 'replacement': {'ts': '1735015867.546059', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '*เขาดู card ที่ dev แก้ไข*\n\n*Project : i live well แก้ไขคำ*\n*AJI-349 :* Request by App Suport\n\n\n\nProject : Mesook แก้ไขคำ\n*TTMWM-184*\nวันที่เปิดการ 2 Dec\nแก้ไขเสร็จ 6 Dec\nไม่ match กับ design', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '8srOa', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เขาดู card ที่ dev แก้ไข', 'style': {'bold': True}}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'Project : i live well แก้ไขคำ', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}, {'type': 'text', 'text': 'AJI-349 :', 'style': {'bold': True}}, {'type': 'text', 'text': ' Request by App Suport\n\n\n\nProject : Mesook แก้ไขคำ\n'}, {'type': 'text', 'text': 'TTMWM-184', 'style': {'bold': True}}, {'type': 'text', 'text': '\nวันที่เปิดการ 2 Dec\nแก้ไขเสร็จ 6 Dec\nไม่ match กับ design'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:52:34,086 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1717988854.352979'}, 'replacement': {'ts': '1717988854.352979', 'type': 'message', 'user': 'U077Z2JC2LQ', 'text': '<@U077Z2JC2LQ> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:53:02,133 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1702627375.373249'}, 'replacement': {'ts': '1702627375.373249', 'type': 'message', 'user': 'U051174HC0N', 'text': '<@U051174HC0N> has left the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_leave', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:53:25,094 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1691465554.759699'}, 'replacement': {'ts': '1691465554.759699', 'type': 'message', 'user': 'U05LB2S1UNT', 'text': '<@U05LB2S1UNT> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:53:43,039 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1683898616.533739'}, 'replacement': {'ts': '1683898616.533739', 'type': 'message', 'user': 'U04QJ1SV0J2', 'text': 'หายไวๆ นะค่ะทุกๆ คนที่มีขีด...:smiling_face_with_3_hearts:', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'WvGYe', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'หายไวๆ นะค่ะทุกๆ คนที่มีขีด...'}, {'type': 'emoji', 'name': 'smiling_face_with_3_hearts', 'unicode': '1f970'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:53:47,439 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1682590661.346049'}, 'replacement': {'ts': '1682590661.346049', 'type': 'message', 'user': 'U04KD337GSK', 'text': 'ข้อบังคับกับวันหยุดบริษัทขอให้พนักงานอ่านด้วยนะค่ะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'C=gV', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ข้อบังคับกับวันหยุดบริษัทขอให้พนักงานอ่านด้วยนะค่ะ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:54:02,852 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1679455438.080739'}, 'replacement': {'ts': '1679455438.080739', 'type': 'message', 'user': 'U04BS0WNJCW', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F04UW6PEZHC', 'created': 1679455426, 'timestamp': 1679455426, 'name': 'แผนธุรกิจ.xlsx', 'title': 'แผนธุรกิจ.xlsx', 'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'filetype': 'xlsx', 'pretty_type': 'Excel spreadsheet', 'user': 'U04BS0WNJCW', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 297250, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04UW6PEZHC/___________________________.xlsx', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04UW6PEZHC/download/___________________________.xlsx', 'media_display_type': 'unknown', 'converted_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UW6PEZHC-582cd04a5e/____________________________converted.pdf', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UW6PEZHC-582cd04a5e/____________________________thumb_pdf.png', 'thumb_pdf_w': 909, 'thumb_pdf_h': 1286, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04UW6PEZHC/___________________________.xlsx', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04UW6PEZHC-a6e104b8ae', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}, {'id': 'F04V2QQGBFC', 'created': 1679455432, 'timestamp': 1679455432, 'name': 'เอกสาร และ ตารางข้อกำหนด ISO13485.xls', 'title': 'เอกสาร และ ตารางข้อกำหนด ISO13485.xls', 'mimetype': 'application/vnd.ms-excel', 'filetype': 'xls', 'pretty_type': 'Excel spreadsheet', 'user': 'U04BS0WNJCW', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 37888, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04V2QQGBFC/_____________________________________________________________________iso13485.xls', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04V2QQGBFC/download/_____________________________________________________________________iso13485.xls', 'media_display_type': 'unknown', 'converted_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04V2QQGBFC-d1e816f5ad/_____________________________________________________________________iso13485_converted.pdf', 'thumb_pdf': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04V2QQGBFC-d1e816f5ad/_____________________________________________________________________iso13485_thumb_pdf.png', 'thumb_pdf_w': 1286, 'thumb_pdf_h': 909, 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04V2QQGBFC/_____________________________________________________________________iso13485.xls', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04V2QQGBFC-ed7ff6badc', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:54:18,506 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1678331840.583839'}, 'replacement': {'ts': '1678331840.583839', 'type': 'message', 'user': 'U04BSASSPTL', 'text': '<@U04KD337GSK>  เรากด record ไม่ได้ครับ ไม่ใช่ host จากลิงค์บริษัท', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'nKiE', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04KD337GSK'}, {'type': 'text', 'text': '  เรากด record ไม่ได้ครับ ไม่ใช่ host จากลิงค์บริษัท'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:54:18,507 - fetch_conversations - INFO - Stored 108 messages for back-offices-archived-archived
2025-05-26 08:54:18,507 - fetch_conversations - INFO - Processing conversation 24/164 (14.6%): bd-po
2025-05-26 08:54:24,930 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1744098635.280719'}, 'replacement': {'ts': '1744098635.280719', 'type': 'message', 'user': 'U08MH4RJ5LZ', 'text': '<@U08MH4RJ5LZ> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:54:44,124 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1707130614.180809'}, 'replacement': {'ts': '1707130614.180809', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'Agenda กับทาง BDMS นะตะ\n<@U04BSASSPTL>\n\n1.Introduction Invitrace Team\n\n2.Research Collaboration\n   -Research Reference &amp; Portfolio\n   -Product Concept &amp; Demonstration\n   -Research Design &amp; Methodology\n   -Timeline\n   -Discussion point\n\n3. Smart IPD\n   scope เชิง overview', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '2WxFQ', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Agenda กับทาง BDMS นะตะ\n'}, {'type': 'user', 'user_id': 'U04BSASSPTL'}, {'type': 'text', 'text': '\n\n1.Introduction Invitrace Team\n\n2.Research Collaboration\n   -Research Reference & Portfolio\n   -Product Concept & Demonstration\n   -Research Design & Methodology\n   -Timeline\n   -Discussion point\n\n3. Smart IPD\n   scope เชิง overview'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:54:58,435 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1688524753.716089'}, 'replacement': {'ts': '1688524753.716089', 'type': 'message', 'user': 'U04CG20VDG8', 'text': 'Excel file ด้วยใช่ไหม', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'rC7Qq', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Excel file ด้วยใช่ไหม'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:55:09,944 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1684725003.911719'}, 'replacement': {'ts': '1684725003.911719', 'type': 'message', 'user': 'U04RR644049', 'text': 'เพราะถ้าเป็น main assessment เห็นว่าพี่ตินอยากให้เอาออก เดี๋ยวมาอัปเดตค่ะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'jRXQ', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เพราะถ้าเป็น main assessment เห็นว่าพี่ตินอยากให้เอาออก เดี๋ยวมาอัปเดตค่ะ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:55:09,946 - fetch_conversations - INFO - Stored 32 messages for bd-po
2025-05-26 08:55:09,946 - fetch_conversations - INFO - Processing conversation 25/164 (15.2%): itele-mtl
2025-05-26 08:55:17,283 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1728016004.131019'}, 'replacement': {'ts': '1728016004.131019', 'type': 'message', 'user': 'U07FUJ50XEV', 'text': 'when you present, you can highlight that this squad will be assigned a Scrum Master when we get active work..', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'LAOjp', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'when you present, you can highlight that this squad will be assigned a Scrum Master when we get active work..'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 08:55:17,284 - fetch_conversations - INFO - Stored 4 messages for itele-mtl
2025-05-26 08:55:17,284 - fetch_conversations - INFO - Processing conversation 26/164 (15.9%): get-health-uat
2025-05-26 08:59:02,586 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.947069'}, 'replacement': {'ts': '**********.947069', 'type': 'message', 'user': 'U06RQT2AFFZ', 'text': '*UAT#1*\n• :done_2:[BO&amp;APP]Support EN Ver. (Article)\n• :done_2:[BO&amp;APP]Support EN Ver. (Reward)\n• :done_2:[BO]Web Responsive (User Management) `*เหลือปรับ Cosmetic เพิ่มเติม`\n• :done_2:[BO]Web Responsive (Account Management) `*เหลือปรับ Cosmetic เพิ่มเติม`\n• :x:[BO]Account Admin Management\n    ◦ ติดเรื่อง Noti expire password \n• :x:[APP]Update user profile \n    ◦ ติดเรื่อง Noti Update profile \n*Next Step*\n• พี่เจต Set กำหนดส่ง Noti เพื่อ UAT บน UAT Envi `ก่อนวันที่ 28 May` ', 'thread_ts': '**********.774059', 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'bWWPq', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'UAT#1', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}]}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'done_2', 'style': {'bold': True}, 'display_team_id': 'T04BC02QBJP', 'display_url': 'https://emoji.slack-edge.com/T04BC02QBJP/done_2/df7f87b626430cc1.gif'}, {'type': 'text', 'text': '[BO&APP]Support EN Ver. (Article)'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'done_2', 'style': {'bold': True}, 'display_team_id': 'T04BC02QBJP', 'display_url': 'https://emoji.slack-edge.com/T04BC02QBJP/done_2/df7f87b626430cc1.gif'}, {'type': 'text', 'text': '[BO&APP]Support EN Ver. (Reward)'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'done_2', 'style': {'bold': True}, 'display_team_id': 'T04BC02QBJP', 'display_url': 'https://emoji.slack-edge.com/T04BC02QBJP/done_2/df7f87b626430cc1.gif'}, {'type': 'text', 'text': '[BO]Web Responsive (User Management) '}, {'type': 'text', 'text': '*เหลือปรับ Cosmetic เพิ่มเติม', 'style': {'code': True}}]}, {'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'done_2', 'style': {'bold': True}, 'display_team_id': 'T04BC02QBJP', 'display_url': 'https://emoji.slack-edge.com/T04BC02QBJP/done_2/df7f87b626430cc1.gif'}, {'type': 'text', 'text': '[BO]Web Responsive (Account Management) '}, {'type': 'text', 'text': '*เหลือปรับ Cosmetic เพิ่มเติม', 'style': {'code': True}}]}, {'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'x', 'unicode': '274c'}, {'type': 'text', 'text': '[BO]Account Admin Management'}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ติดเรื่อง Noti expire password '}]}], 'style': 'bullet', 'indent': 1, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'x', 'unicode': '274c'}, {'type': 'text', 'text': '[APP]Update user profile '}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ติดเรื่อง Noti Update profile '}]}], 'style': 'bullet', 'indent': 1, 'border': 0}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '\n'}, {'type': 'text', 'text': 'Next Step', 'style': {'bold': True}}, {'type': 'text', 'text': '\n'}]}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'พี่เจต Set กำหนดส่ง Noti เพื่อ UAT บน UAT Envi '}, {'type': 'text', 'text': 'ก่อนวันที่ 28 May', 'style': {'code': True}}, {'type': 'text', 'text': ' '}]}], 'style': 'bullet', 'indent': 0, 'border': 0}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'thread_broadcast', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:01:54,320 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.219279'}, 'replacement': {'ts': '**********.219279', 'type': 'message', 'user': 'U04P87WDQN8', 'text': '<@U05QLD2D72S> <@U04RSNVNV4M>\nพี่เกดเจอ ไม่ urgent ฝาก assign dev ให้ปรับหน่อยครับ\n\n[GetHealth][App] หน้าก้าว กับ น้ำ ข้อความเกินช่อง (EN)\n<https://app.clickup.com/t/25550248/INV-7156>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'HX9fz', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U05QLD2D72S'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04RSNVNV4M'}, {'type': 'text', 'text': '\nพี่เกดเจอ ไม่ urgent ฝาก assign dev ให้ปรับหน่อยครับ\n\n[GetHealth][App] หน้าก้าว กับ น้ำ ข้อความเกินช่อง (EN)\n'}, {'type': 'link', 'url': 'https://app.clickup.com/t/25550248/INV-7156'}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': '+1', 'users': ['U04LM9MDQP9'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:04:59,929 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.712299'}, 'replacement': {'ts': '**********.712299', 'type': 'message', 'user': 'U04BS7QUBQT', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '6cFAE', 'elements': [{'type': 'rich_text_section', 'elements': []}]}], 'attachments': [], 'files': [{'id': 'F051K0LTA8Z', 'created': **********, 'timestamp': **********, 'name': 'Screenshot_20230331_133251_Get Health UAT.jpg', 'title': 'Screenshot_20230331_133251_Get Health UAT.jpg', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04BS7QUBQT', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 48667, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F051K0LTA8Z/screenshot_20230331_133251_get_health_uat.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F051K0LTA8Z/download/screenshot_20230331_133251_get_health_uat.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_360.jpg', 'thumb_360_w': 162, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_480.jpg', 'thumb_480_w': 216, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_720.jpg', 'thumb_720_w': 324, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_800.jpg', 'thumb_800_w': 360, 'thumb_800_h': 800, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_960.jpg', 'thumb_960_w': 432, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F051K0LTA8Z-3b0c4c6766/screenshot_20230331_133251_get_health_uat_1024.jpg', 'thumb_1024_w': 461, 'thumb_1024_h': 1024, 'original_w': 720, 'original_h': 1600, 'thumb_tiny': 'AwAwABVpspjkYUH3ammwnP8Ac/76rUBJJ+XGDwfWnAZFO4rGP/Z0/wDsf99Uf2dcf7H/AH1WwRSUXGZzXkofAbGGIPA5q0kzMR8wNZUhPmOO2408NjucVI7GyTRVC1lchhuJAxjPap/MamIzJFbzG+U/ePakw2MYP5Vu5oz1oHcy7bczOSDzjtU+D6GrtHFAj//Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F051K0LTA8Z/screenshot_20230331_133251_get_health_uat.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F051K0LTA8Z-42d46c2526', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:04:59,931 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.783729'}, 'replacement': {'ts': '**********.783729', 'type': 'message', 'user': 'U04TN6W9G04', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'sRyvg', 'elements': [{'type': 'rich_text_section', 'elements': []}]}], 'attachments': [], 'files': [{'id': 'F04UD3Z8DCH', 'created': **********, 'timestamp': **********, 'name': 'Screenshot_20230315_140322_Get Health UAT.jpg', 'title': 'Screenshot_20230315_140322_Get Health UAT.jpg', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U04TN6W9G04', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 93680, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F04UD3Z8DCH/screenshot_20230315_140322_get_health_uat.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F04UD3Z8DCH/download/screenshot_20230315_140322_get_health_uat.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_360.jpg', 'thumb_360_w': 162, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_480.jpg', 'thumb_480_w': 216, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_720.jpg', 'thumb_720_w': 324, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1778, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_960.jpg', 'thumb_960_w': 432, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F04UD3Z8DCH-475047ca75/screenshot_20230315_140322_get_health_uat_1024.jpg', 'thumb_1024_w': 461, 'thumb_1024_h': 1024, 'original_w': 1080, 'original_h': 2400, 'thumb_tiny': 'AwAwABWrSU7bx1FPSBmTecbTwDnpVNiSGFGwCEIB5HvRsb+6a0Fuo0VUlO4gDkDNL9stvT/x2i4WIJLGVfuEOPyNVZDNCCGBGezDitstjqKaWVhgjI9DikMxchvvZH0o+T1atVrOBzkx7focUn2CD+6f++jQBYIz3pMf7VMZJCxxJgemKTZL/wA9f0qLvsOxMKKZGGXO5t1OzTQj/9k=', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F04UD3Z8DCH/screenshot_20230315_140322_get_health_uat.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F04UD3Z8DCH-f97a695f21', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:04:59,932 - fetch_conversations - INFO - Stored 382 messages for get-health-uat
2025-05-26 09:04:59,932 - fetch_conversations - INFO - Processing conversation 27/164 (16.5%): 7-pm-project-managment
2025-05-26 09:05:02,849 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.218819'}, 'replacement': {'ts': '**********.218819', 'type': 'message', 'user': 'U04BS7QUBQT', 'text': 'Noted krub ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'hXhF', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Noted krub '}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:05:02,849 - fetch_conversations - INFO - Stored 1 messages for 7-pm-project-managment
2025-05-26 09:05:02,849 - fetch_conversations - INFO - Processing conversation 28/164 (17.1%): สั่งข้าวเรียกด้วย
2025-05-26 09:07:14,098 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1746010863.220619'}, 'replacement': {'ts': '1746010863.220619', 'type': 'message', 'user': 'U056V5D299V', 'text': ':tea: Coconut matcha\n50 baht\n\n<@U04RNH03CHH>\n<@U08365WL11Q> \n<@U0511KJSS0K>\n<@U05MSM3FT2A> ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'FL4SE', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'emoji', 'name': 'tea', 'unicode': '1f375'}, {'type': 'text', 'text': ' Coconut matcha\n50 baht\n\n'}, {'type': 'user', 'user_id': 'U04RNH03CHH'}, {'type': 'text', 'text': '\n'}, {'type': 'user', 'user_id': 'U08365WL11Q'}, {'type': 'text', 'text': ' \n'}, {'type': 'user', 'user_id': 'U0511KJSS0K'}, {'type': 'text', 'text': '\n'}, {'type': 'user', 'user_id': 'U05MSM3FT2A'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [{'id': 'F08Q3GH7NBG', 'created': 1746010617, 'timestamp': 1746010617, 'name': 'IMG_9953.jpg', 'title': 'IMG_9953', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U056V5D299V', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 71633, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F08Q3GH7NBG/img_9953.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F08Q3GH7NBG/download/img_9953.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_360.jpg', 'thumb_360_w': 266, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_480.jpg', 'thumb_480_w': 354, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_720.jpg', 'thumb_720_w': 531, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1085, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_960.jpg', 'thumb_960_w': 708, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08Q3GH7NBG-6281004d6b/img_9953_1024.jpg', 'thumb_1024_w': 755, 'thumb_1024_h': 1024, 'original_w': 885, 'original_h': 1200, 'thumb_tiny': 'AwAwACOP7PN/zyb8qkjhbYweF9x6HFadVmuMu6MnA9+tVcmxU8lv+eL/AJUz7PN/zyb8qtLcqoYpHjHTrzVqJ/MjDEYzRcLGV9nm/wCeTflR9nm/55N+VbFFHMHKFZ0u7zXGD8xP0xmtGqxt3LudykNng1JRXkyrgfMuR1zjNXbfPkrnrUH2Nj/EOAQCKsxKUjCk5IoAdRRRQAUznp3/ABpQSSRg/X1paAG9P8mnLnHNGKKAFopM+9GR/eFAH//Z', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F08Q3GH7NBG/img_9953.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F08Q3GH7NBG-c195eb1d65', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:07:45,254 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1685610107.820769'}, 'replacement': {'ts': '1685610107.820769', 'type': 'message', 'user': 'U0579MH397C', 'text': '<@U0579MH397C> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:07:45,255 - fetch_conversations - INFO - Stored 106 messages for สั่งข้าวเรียกด้วย
2025-05-26 09:07:45,255 - fetch_conversations - INFO - Processing conversation 29/164 (17.7%): get-health
2025-05-26 09:07:46,688 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.181229'}, 'replacement': {'ts': '**********.181229', 'type': 'message', 'user': 'U04CG20VDG8', 'text': '<@U04CG20VDG8> has joined the channel', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'channel_join', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:07:46,688 - fetch_conversations - INFO - Stored 0 messages for get-health
2025-05-26 09:07:46,688 - fetch_conversations - INFO - Processing conversation 30/164 (18.3%): proj_telecare
2025-05-26 09:08:38,207 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '**********.905769'}, 'replacement': {'ts': '**********.905769', 'type': 'message', 'user': 'U04LM9MDQP9', 'text': '<@U04RSN3783X> ทาง รพ แจ้งมาว่า "เครื่อง Telemed Disk เต็ม" อันนี้ทางเราจะต้องเข้าดำเนินการยังไงหรอคะ ต้องเพิ่ม? CC: <@U04BBQ80F2B>\n(ในเมล แจ้งเครื่อง Telemed Disk เต็ม ส่งมาวันที่ 15.05.25)', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '9Dgwg', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04RSN3783X'}, {'type': 'text', 'text': ' ทาง รพ แจ้งมาว่า "เครื่อง Telemed Disk เต็ม" อันนี้ทางเราจะต้องเข้าดำเนินการยังไงหรอคะ ต้องเพิ่ม? CC: '}, {'type': 'user', 'user_id': 'U04BBQ80F2B'}, {'type': 'text', 'text': '\n(ในเมล แจ้งเครื่อง Telemed Disk เต็ม ส่งมาวันที่ 15.05.25)'}]}]}], 'attachments': [], 'files': [{'id': 'F08S3E9C8UF', 'created': 1747304979, 'timestamp': 1747304979, 'name': 'image.png', 'title': 'image.png', 'mimetype': 'image/png', 'filetype': 'png', 'pretty_type': 'PNG', 'user': 'U04LM9MDQP9', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 358675, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F08S3E9C8UF/image.png', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F08S3E9C8UF/download/image.png', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_64.png', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_80.png', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_360.png', 'thumb_360_w': 360, 'thumb_360_h': 253, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_480.png', 'thumb_480_w': 480, 'thumb_480_h': 338, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_160.png', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_720.png', 'thumb_720_w': 720, 'thumb_720_h': 506, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_800.png', 'thumb_800_w': 800, 'thumb_800_h': 563, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_960.png', 'thumb_960_w': 960, 'thumb_960_h': 675, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F08S3E9C8UF-fa1b298aac/image_1024.png', 'thumb_1024_w': 1024, 'thumb_1024_h': 720, 'original_w': 1426, 'original_h': 1003, 'thumb_tiny': 'AwAhADDRON33vwpdy+oqjc3xhmZBHnHfdUX9pt/zx/8AHjQBp7h6ilBz0rLGpt/zx/8AHzS/2o3/ADxH/fVAGnRWX/ax/wCeI/76qzZXhumcFNu0DvmgDO1H/j8k/D+VVc1o3lpPLcuyR5U4wcj0qv8A2fc/88/1FAFcUoqwLC6H/LL/AMeFKLC6H/LL/wAeFAFVuDWjo/35foKrmwuj/wAsv/HhV3TbaWBpDKu3IGOaAL9FFFABRRRQAUUUUAf/2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F08S3E9C8UF/image.png', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F08S3E9C8UF-65321a5d7a', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:09:30,729 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1737111396.780349'}, 'replacement': {'ts': '1737111396.780349', 'type': 'message', 'user': 'U076B3M7877', 'text': 'ขอบคุณครับ', 'thread_ts': '1737111366.495219', 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'F3Gse', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ขอบคุณครับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'thread_broadcast', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:11:09,400 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1729484208.635149'}, 'replacement': {'ts': '1729484208.635149', 'type': 'message', 'user': 'U07FUJ50XEV', 'text': '<@U04CG20VDG8> FYI', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'JXTE9', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U04CG20VDG8'}, {'type': 'text', 'text': ' FYI'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:12:18,166 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1708070616.799809'}, 'replacement': {'ts': '1708070616.799809', 'type': 'message', 'user': 'U04LM9MDQP9', 'text': '<@U06C7PL1DB5>  ฝากหน่อยน้า\nความหมาย\n//correct คือ mapping ให้แล้ว\n// ไม่มี clinic บน prod คือ เราไม่ได้ mapping ให้\n\nPLD,PLK,PLC ให้ลูกค้าเช็คและใส่มาให้ก่อน\n\nปล. จะทำให้อีกนึง อีกรอบเดียว', 'thread_ts': '1707798333.165929', 'parent_user_id': 'U06C7PL1DB5', 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'ssRUU', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'user', 'user_id': 'U06C7PL1DB5'}, {'type': 'text', 'text': '  ฝากหน่อยน้า\nความหมาย\n//correct คือ mapping ให้แล้ว\n// ไม่มี clinic บน prod คือ เราไม่ได้ mapping ให้\n\nPLD,PLK,PLC ให้ลูกค้าเช็คและใส่มาให้ก่อน\n\nปล. จะทำให้อีกนึง อีกรอบเดียว'}]}]}], 'attachments': [], 'files': [{'id': 'F06KU1L301W', 'created': 1708070347, 'timestamp': 1708070347, 'name': 'mapping clinic prod.php', 'title': 'mapping clinic prod.php', 'mimetype': 'text/plain', 'filetype': 'php', 'pretty_type': 'PHP', 'user': 'U04LM9MDQP9', 'user_team': 'T04BC02QBJP', 'editable': True, 'size': 10144, 'mode': 'snippet', 'is_external': False, 'external_type': '', 'is_public': True, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F06KU1L301W/mapping_clinic_prod.php', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F06KU1L301W/download/mapping_clinic_prod.php', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06KU1L301W/mapping_clinic_prod.php', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F06KU1L301W-9ba11c6daf', 'edit_link': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F06KU1L301W/mapping_clinic_prod.php/edit', 'preview': '<?php\nnamespace Database\\Seeders;\n\nuse App\\Models\\Clinic;\n', 'preview_highlight': '<div class="CodeMirror cm-s-default CodeMirrorServer">\n<div class="CodeMirror-code">\n<div><pre><span class="cm-meta">&lt;?php</span></pre></div>\n<div><pre><span class="cm-keyword">namespace</span> <span class="cm-def">Database</span><span class="cm-def">\\Seeders</span>;</pre></div>\n<div><pre>&#8203;</pre></div>\n<div><pre><span class="cm-keyword">use</span> <span class="cm-variable">App</span><span class="cm-variable">\\Models</span><span class="cm-variable">\\Clinic</span>;</pre></div>\n</div>\n</div>\n', 'lines': 484, 'lines_more': 479, 'preview_is_truncated': True, 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [{'name': 'saluting_face', 'users': ['U06C7PL1DB5'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': 'thread_broadcast', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:15:11,474 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1697471435.212849'}, 'replacement': {'ts': '1697471435.212849', 'type': 'message', 'user': 'U04RKNHDWBX', 'text': 'Android หน้าconsent แก้ไขไห้แล้วครับ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'ZnOo5', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Android หน้าconsent แก้ไขไห้แล้วครับ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:15:59,869 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1686905254.029719'}, 'replacement': {'ts': '1686905254.029719', 'type': 'message', 'user': 'U04RSN3783X', 'text': 'กำลังนึกว่า หรือจะเป็นเรื่องเดียวกัน\n\nคือ สร้าง virtual clinic ที่ เห็น ทุกกิจกรรม ใน รพ นั้นไปเลย\n\nแล้วค่อยเอา role พยาบาล(เวชระเบียน) กับ เภสัช เข้าไปใส่', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'ynT', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'กำลังนึกว่า หรือจะเป็นเรื่องเดียวกัน\n\nคือ สร้าง virtual clinic ที่ เห็น ทุกกิจกรรม ใน รพ นั้นไปเลย\n\nแล้วค่อยเอา role พยาบาล(เวชระเบียน) กับ เภสัช เข้าไปใส่'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:16:13,095 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1685590133.821119'}, 'replacement': {'ts': '1685590133.821119', 'type': 'message', 'user': 'U04TN6W9G04', 'text': '555 คุณตั้มมมมม เรื่องคอลค่าาาา', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'Fk7', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '555 คุณตั้มมมมม เรื่องคอลค่าาาา'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:16:13,096 - fetch_conversations - INFO - Stored 331 messages for proj_telecare
2025-05-26 09:16:13,096 - fetch_conversations - INFO - Processing conversation 31/164 (18.9%): ds-gang
2025-05-26 09:16:59,181 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1747811331.719349'}, 'replacement': {'ts': '1747811331.719349', 'type': 'message', 'user': 'USLACKBOT', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '+Qn/e', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Parin Kittipongdaja made updates to a canvas tab: '}, {'type': 'canvas', 'file_id': 'F064Z02SW0H'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': 'tabbed_canvas_updated', 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:17:40,641 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1734701816.257259'}, 'replacement': {'ts': '1734701816.257259', 'type': 'message', 'user': 'U07PV66KCCC', 'text': '<https://www.facebook.com/share/p/1BcQLVjHCJ/?|https://www.facebook.com/share/p/1BcQLVjHCJ/?>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'TY72G', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www.facebook.com/share/p/1BcQLVjHCJ/?', 'text': 'https://www.facebook.com/share/p/1BcQLVjHCJ/?'}]}]}], 'attachments': [{'from_url': 'https://www.facebook.com/share/p/1BcQLVjHCJ/', 'service_icon': 'https://static.xx.fbcdn.net/rsrc.php/yB/r/2sFJRNmJ5OP.ico', 'thumb_url': 'https://scontent-iad3-2.xx.fbcdn.net/v/t39.30808-6/471272967_1042300921273436_4942786138723541621_n.jpg?cstp=mx1638x2048&ctp=p600x600&_nc_cat=100&ccb=1-7&_nc_sid=cae128&_nc_ohc=i8H66RWTZTAQ7kNvgEqZ9wO&_nc_zt=23&_nc_ht=scontent-iad3-2.xx&_nc_gid=AnYv2nrMrVKt1oBZnlbf7Dn&oh=00_AYAFV1Nfp3NTeOFpXYWoXXZqsONSTEkh3dMdndRR-acWVw&oe=676B3B42', 'thumb_width': 600, 'thumb_height': 750, 'id': 1, 'original_url': 'https://www.facebook.com/share/p/1BcQLVjHCJ/?', 'fallback': 'Skooldio', 'text': ':gift: AI Club On Ground ครั้งที่ 5\n“Generative Joy: Unwrapping Generative AI Applications” :christmas_tree:\n:santa::skin-tone-2: AI Club ขอเชิญชวนทุกคนมาแกะกล่องของขวัญจาก Skooldio x GDG Cloud กัน!.\n.\n:sparkles: แกะกล่องความรู้ใหม่ไปใน Meetup...', 'title': 'Skooldio', 'title_link': 'https://www.facebook.com/share/p/1BcQLVjHCJ/', 'service_name': 'facebook.com'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:17:40,642 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1726461960.462409'}, 'replacement': {'ts': '1726461960.462409', 'type': 'message', 'user': 'U06RG35SFA5', 'text': 'เยอะมาก', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '5Uobj', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เยอะมาก'}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': 'cat-urgent', 'users': ['U076E0UJEJE'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:17:45,255 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1722495049.008019'}, 'replacement': {'ts': '1722495049.008019', 'type': 'message', 'user': 'U076E0UJEJE', 'text': 'พี่ไอซ์ค่ะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '7ws3L', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'พี่ไอซ์ค่ะ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:17:48,105 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1718276912.112709'}, 'replacement': {'ts': '1718276912.112709', 'type': 'message', 'user': 'U076E0UJEJE', 'text': 'รับทราบค่ะ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'qw/rw', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'รับทราบค่ะ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:18:23,552 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1707877022.042359'}, 'replacement': {'ts': '1707877022.042359', 'type': 'message', 'user': 'U04BKNM9HCN', 'text': 'โอเคครับบบ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'o1SiE', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'โอเคครับบบ'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:19:20,537 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1700394593.581399'}, 'replacement': {'ts': '1700394593.581399', 'type': 'message', 'user': 'U0514SCCYH3', 'text': 'อ้อโอเคครับ:+1:', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': '1+qYv', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'อ้อโอเคครับ'}, {'type': 'emoji', 'name': '+1', 'unicode': '1f44d'}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': '+1', 'users': ['U04BKNM9HCN'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:20:08,175 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1696238843.705909'}, 'replacement': {'ts': '1696238843.705909', 'type': 'message', 'user': 'U04BKNM9HCN', 'text': 'เหยยย ดี', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'HXE', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'เหยยย ดี'}]}]}], 'attachments': [], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:20:42,036 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1692358596.632329'}, 'replacement': {'ts': '1692358596.632329', 'type': 'message', 'user': 'U055Q3FDWN9', 'text': '', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [], 'attachments': [], 'files': [{'id': 'F05NETLQM3M', 'created': 1692358589, 'timestamp': 1692358589, 'name': 'IMG_6440.jpg', 'title': 'IMG_6440', 'mimetype': 'image/jpeg', 'filetype': 'jpg', 'pretty_type': 'JPEG', 'user': 'U055Q3FDWN9', 'user_team': 'T04BC02QBJP', 'editable': False, 'size': 396594, 'mode': 'hosted', 'is_external': False, 'external_type': '', 'is_public': False, 'public_url_shared': False, 'display_as_bot': False, 'username': '', 'url_private': 'https://files.slack.com/files-pri/T04BC02QBJP-F05NETLQM3M/img_6440.jpg', 'url_private_download': 'https://files.slack.com/files-pri/T04BC02QBJP-F05NETLQM3M/download/img_6440.jpg', 'media_display_type': 'unknown', 'thumb_64': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_64.jpg', 'thumb_80': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_80.jpg', 'thumb_360': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_360.jpg', 'thumb_360_w': 288, 'thumb_360_h': 360, 'thumb_480': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_480.jpg', 'thumb_480_w': 384, 'thumb_480_h': 480, 'thumb_160': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_160.jpg', 'thumb_720': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_720.jpg', 'thumb_720_w': 576, 'thumb_720_h': 720, 'thumb_800': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_800.jpg', 'thumb_800_w': 800, 'thumb_800_h': 1000, 'thumb_960': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_960.jpg', 'thumb_960_w': 768, 'thumb_960_h': 960, 'thumb_1024': 'https://files.slack.com/files-tmb/T04BC02QBJP-F05NETLQM3M-53cdf7ae8a/img_6440_1024.jpg', 'thumb_1024_w': 819, 'thumb_1024_h': 1024, 'original_w': 1831, 'original_h': 2289, 'thumb_tiny': 'AwAwACakMDrS8c/d/M001MjYQZWutsViMgc/d9etAxjoODn/AOtUoOCMqD7mmtzjIx9Km9ykiLFJipCvoaNtMdhmKeu0HPX8KfHgI4Zc5HHFPiUKrFl6jjNS5CGbl9B/3yKZgevNToE+zsMAknimJjypDt5GM0uaxQzFGKcBS4qy7Cov75dwOwnGKkLbgqtnHzdfanwSIi5K7jnP0plw/nOirHgKOOcVhJO5m0yK3C+W+eT2+lEasUI2tkjBwKQxFSduOR71PCxVMOCcHgg0JMajcj2kAbgR+FFTTTGVQMYwc9c1Ditltqbpaan/2Q==', 'permalink': 'https://invitraceworkspace.slack.com/files/U07PV66KCCC/F05NETLQM3M/img_6440.jpg', 'permalink_public': 'https://slack-files.com/T04BC02QBJP-F05NETLQM3M-cc1ced07b6', 'is_starred': False, 'skipped_shares': True, 'has_rich_preview': False, 'file_access': 'visible'}], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:21:06,527 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1688352381.510329'}, 'replacement': {'ts': '1688352381.510329', 'type': 'message', 'user': 'U055Q3FDWN9', 'text': '<https://overapi.com/|https://overapi.com/>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'bKGS', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://overapi.com/', 'text': 'https://overapi.com/'}]}]}], 'attachments': [{'from_url': 'https://overapi.com/', 'service_icon': 'https://overapi.com/favicon.ico', 'id': 1, 'original_url': 'https://overapi.com/', 'fallback': 'OverAPI.com | Collecting all the cheat sheets', 'text': '<http://OverAPI.com|OverAPI.com> is a site collecting all the cheatsheets,all!', 'title': 'OverAPI.com | Collecting all the cheat sheets', 'title_link': 'https://overapi.com/', 'service_name': 'overapi.com'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:21:20,302 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1686553155.571119'}, 'replacement': {'ts': '1686553155.571119', 'type': 'message', 'user': 'U055Q3FDWN9', 'text': '<https://github.com/robocorp/rpaframework>', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'EITjF', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://github.com/robocorp/rpaframework'}]}]}], 'attachments': [{'id': 1, 'color': '24292f', 'bot_id': 'B0577NHQZM3', 'app_unfurl_url': 'https://github.com/robocorp/rpaframework', 'is_app_unfurl': True, 'app_id': 'A01BP7R4KNY', 'fallback': 'robocorp/rpaframework', 'text': 'Collection of open-source libraries and tools for Robotic Process Automation (RPA), designed to be used with both Robot Framework and Python', 'title': 'robocorp/rpaframework', 'fields': [{'value': '<https://www.rpaframework.org/>', 'title': 'Website', 'short': True}, {'value': '812', 'title': 'Stars', 'short': True}]}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:21:20,303 - fetch_conversations - INFO - Stored 200 messages for ds-gang
2025-05-26 09:21:20,303 - fetch_conversations - INFO - Processing conversation 32/164 (19.5%): proj_slh-uat
2025-05-26 09:22:16,303 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1736334647.361089'}, 'replacement': {'ts': '1736334647.361089', 'type': 'message', 'user': 'U04RSN3783X', 'text': 'By next few days\n\nDev team please address all SLH issues and catch up foundation requirements. Be prepared for prod RC.\n\nPO please revise and breakdown story so that they can be delivered within Sprint. Any extra stories should be added to current sprint Please let SM know.\n\nCc: <@U07A0JBTJH5> <@U05LXJD18SD> <@U04RNH03CHH> <@U06UQ67FM2P> <@U07LDK7BDN1> ', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'W8ooc', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'By next few days\n\n'}, {'type': 'text', 'text': 'Dev team'}, {'type': 'text', 'text': ' please '}, {'type': 'text', 'text': 'address all SLH issues and catch up foundation requirements. Be prepared for prod RC.'}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'PO '}, {'type': 'text', 'text': 'please '}, {'type': 'text', 'text': 'revise and breakdown story so that they can be delivered within Sprint. Any extra stories should be added to current sprint Please let SM know.'}, {'type': 'text', 'text': '\n\n'}, {'type': 'text', 'text': 'Cc: '}, {'type': 'user', 'user_id': 'U07A0JBTJH5'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U05LXJD18SD'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U04RNH03CHH'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U06UQ67FM2P'}, {'type': 'text', 'text': ' '}, {'type': 'user', 'user_id': 'U07LDK7BDN1'}, {'type': 'text', 'text': ' '}]}]}], 'attachments': [], 'files': [], 'reactions': [{'name': 'pray::skin-tone-6', 'users': ['U07A0JBTJH5'], 'count': 1}, {'name': 'pray::skin-tone-2', 'users': ['U077A5MFK0T'], 'count': 1}, {'name': 'hand_with_index_finger_and_thumb_crossed::skin-tone-2', 'users': ['U077A5MFK0T'], 'count': 1}, {'name': 'meow_heart', 'users': ['U077A5MFK0T'], 'count': 1, 'url': 'https://emoji.slack-edge.com/T04BC02QBJP/meow_heart/fb96fc56e6d670d3.png'}, {'name': 'pray', 'users': ['U04RNH03CHH'], 'count': 1}, {'name': 'saluting_face', 'users': ['U06UQ67FM2P'], 'count': 1}], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:22:36,055 - mongo_handler - ERROR - Error in batch storing messages: {'replaceOne': {'filter': {'ts': '1716742745.334619'}, 'replacement': {'ts': '1716742745.334619', 'type': 'message', 'user': 'U04RSN3783X', 'text': '• SLH Mobile 1.0.5 (80) on TestFlight &amp; Test PlayStore\n• SLH UAT 1.0.5 (80) on TestFlight &amp; Test PlayStore\n• Web + BackOffice + API update Dev &gt; `UAT`  + Prod\n• Remove Sentry ออกจากทุกโปรเจค แล้ว\n• ปรับ Endpoint เป็น ชื่อใหม่ ทั้งหมดแล้ว\n    ◦ <https://www-test.saintlouis.or.th/> =&gt; <https://public-api-test.saintlouis.or.th>\n    ◦ <https://www2.saintlouis.or.th/> =&gt; <https://public-api.saintlouis.or.th>\n    ◦ SLH UAT =&gt; <https://public-api-test.saintlouis.or.th>\n    ◦ SLH Mobile =&gt; <https://public-api.saintlouis.or.th> \n• upload `dist` ขึ้น SLH GitHub (ผ่านทาง CI/CD แล้ว) <@U04BS7QUBQT> \n    ◦ <https://github.com/stlouis-dev/deploy-web-slh>\n    ◦ <https://github.com/stlouis-dev/deploy-web-admin>\n', 'thread_ts': None, 'parent_user_id': None, 'reply_count': 0, 'blocks': [{'type': 'rich_text', 'block_id': 'n41ky', 'elements': [{'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'SLH Mobile 1.0.5 (80) on TestFlight & Test PlayStore'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'SLH UAT 1.0.5 (80) on TestFlight & Test PlayStore'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Web + BackOffice + API update Dev > '}, {'type': 'text', 'text': 'UAT', 'style': {'code': True}}, {'type': 'text', 'text': '  + Prod'}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': '\n'}]}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'Remove Sentry ออกจากทุกโปรเจค แล้ว'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'ปรับ Endpoint เป็น ชื่อใหม่ ทั้งหมดแล้ว'}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www-test.saintlouis.or.th/'}, {'type': 'text', 'text': ' => '}, {'type': 'link', 'url': 'https://public-api-test.saintlouis.or.th'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://www2.saintlouis.or.th/'}, {'type': 'text', 'text': ' => '}, {'type': 'link', 'url': 'https://public-api.saintlouis.or.th'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'SLH UAT => '}, {'type': 'link', 'url': 'https://public-api-test.saintlouis.or.th'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'SLH Mobile => '}, {'type': 'link', 'url': 'https://public-api.saintlouis.or.th'}, {'type': 'text', 'text': ' '}]}], 'style': 'bullet', 'indent': 1, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'text', 'text': 'upload '}, {'type': 'text', 'text': 'dist', 'style': {'code': True}}, {'type': 'text', 'text': ' ขึ้น SLH GitHub (ผ่านทาง CI/CD แล้ว) '}, {'type': 'user', 'user_id': 'U04BS7QUBQT'}, {'type': 'text', 'text': ' '}]}], 'style': 'bullet', 'indent': 0, 'border': 0}, {'type': 'rich_text_list', 'elements': [{'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://github.com/stlouis-dev/deploy-web-slh'}]}, {'type': 'rich_text_section', 'elements': [{'type': 'link', 'url': 'https://github.com/stlouis-dev/deploy-web-admin'}]}], 'style': 'bullet', 'indent': 1, 'border': 0}, {'type': 'rich_text_section', 'elements': []}]}], 'attachments': [{'from_url': 'https://www-test.saintlouis.or.th/', 'id': 1, 'original_url': 'https://www-test.saintlouis.or.th/', 'fallback': 'โรงพยาบาลเซนต์หลุยส์ | Saint Louis Hospital', 'text': 'มั่นใจ..กับการวินิจฉัยที่ตรงจุดโดยแพทย์และทีมงานผู้เชี่ยวชาญ', 'title': 'โรงพยาบาลเซนต์หลุยส์ | Saint Louis Hospital', 'title_link': 'https://www-test.saintlouis.or.th/', 'service_name': 'www-test.saintlouis.or.th'}], 'files': [], 'reactions': [], 'edited': None, 'bot_id': None, 'subtype': None, 'hidden': False, 'is_starred': False}, 'upsert': True}} is not a valid request
2025-05-26 09:22:56,678 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 09:22:56,679 - __main__ - INFO - Export interrupted by user
2025-05-26 09:23:02,668 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 09:23:02,670 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 09:23:02,670 - auth - INFO - Testing Slack authentication...
2025-05-26 09:23:03,042 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 09:23:03,421 - auth - INFO - Team: Invitrace (invitraceworkspace)
2025-05-26 09:23:03,802 - auth - INFO - Validating token permissions...
2025-05-26 09:23:04,193 - auth - INFO - ✓ Conversations access validated
2025-05-26 09:23:04,591 - auth - INFO - ✓ Users access validated
2025-05-26 09:23:04,591 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 09:23:04,591 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 09:23:04,592 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 09:23:04,592 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 09:23:04,617 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 09:23:04,617 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 09:23:04,617 - __main__ - INFO - MongoDB connection established
2025-05-26 09:23:04,617 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 09:23:04,617 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 09:23:04,617 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 09:23:04,617 - slack_client - INFO - Fetching conversations...
2025-05-26 09:23:06,321 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 09:23:07,836 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 09:23:09,283 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 09:23:10,797 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 09:23:12,275 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 09:23:13,743 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 09:23:15,287 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 09:23:15,287 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 09:23:15,287 - fetch_conversations - INFO - Found 164 conversations to process
2025-05-26 09:23:15,287 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 09:23:17,182 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 09:23:17,182 - __main__ - INFO - Export interrupted by user
2025-05-26 11:10:16,212 - __main__ - INFO - === Slack Chat History Exporter Started ===
2025-05-26 11:10:16,212 - __main__ - INFO - Step 1: Authenticating with Slack...
2025-05-26 11:10:16,212 - auth - INFO - Testing Slack authentication...
2025-05-26 11:10:16,735 - auth - INFO - Successfully authenticated as parin.ki on team Invitrace
2025-05-26 11:10:17,173 - auth - INFO - Team: Invitrace (invitraceworkspace)
2025-05-26 11:10:17,548 - auth - INFO - Validating token permissions...
2025-05-26 11:10:18,052 - auth - INFO - ✓ Conversations access validated
2025-05-26 11:10:18,471 - auth - INFO - ✓ Users access validated
2025-05-26 11:10:18,471 - __main__ - INFO - Authenticated as: parin.ki on team Invitrace
2025-05-26 11:10:18,471 - __main__ - INFO - Step 2: Initializing Slack conversation client...
2025-05-26 11:10:18,471 - __main__ - INFO - Step 3: Connecting to MongoDB...
2025-05-26 11:10:18,471 - mongo_handler - INFO - Connecting to MongoDB: mongodb://localhost:27017/
2025-05-26 11:10:18,513 - mongo_handler - INFO - Successfully connected to MongoDB
2025-05-26 11:10:18,513 - mongo_handler - INFO - Using database: slack_chat_history
2025-05-26 11:10:18,513 - __main__ - INFO - MongoDB connection established
2025-05-26 11:10:18,514 - __main__ - INFO - Step 4: Initializing conversation fetcher...
2025-05-26 11:10:18,514 - __main__ - INFO - Step 5: Starting conversation and message fetch...
2025-05-26 11:10:18,514 - fetch_conversations - INFO - Starting conversation fetch process...
2025-05-26 11:10:18,514 - fetch_conversations - INFO - Fetching and storing users list...
2025-05-26 11:10:18,514 - slack_client - INFO - Fetching all users...
2025-05-26 11:10:20,388 - slack_client - INFO - Fetched 200 users (total: 200)
2025-05-26 11:10:21,955 - slack_client - INFO - Fetched 59 users (total: 259)
2025-05-26 11:10:21,955 - slack_client - INFO - Finished fetching users. Total: 259
2025-05-26 11:10:22,013 - mongo_handler - INFO - Stored 259 users in users_list collection
2025-05-26 11:10:22,013 - fetch_conversations - INFO - Stored 259 users in users_list collection
2025-05-26 11:10:22,013 - slack_client - INFO - Fetching conversations...
2025-05-26 11:10:24,025 - slack_client - INFO - Fetched 41 conversations (total: 41)
2025-05-26 11:10:25,672 - slack_client - INFO - Fetched 5 conversations (total: 46)
2025-05-26 11:10:27,432 - slack_client - INFO - Fetched 5 conversations (total: 51)
2025-05-26 11:10:29,087 - slack_client - INFO - Fetched 13 conversations (total: 64)
2025-05-26 11:10:30,928 - slack_client - INFO - Fetched 12 conversations (total: 76)
2025-05-26 11:10:32,749 - slack_client - INFO - Fetched 7 conversations (total: 83)
2025-05-26 11:10:34,363 - slack_client - INFO - Fetched 81 conversations (total: 164)
2025-05-26 11:10:34,363 - slack_client - INFO - Finished fetching conversations. Total: 164
2025-05-26 11:10:34,363 - fetch_conversations - INFO - Storing conversations list...
2025-05-26 11:10:34,398 - mongo_handler - INFO - Stored 164 conversations in conversations_list collection
2025-05-26 11:10:34,399 - fetch_conversations - INFO - Stored 164 conversations in conversations_list collection
2025-05-26 11:10:34,399 - fetch_conversations - INFO - Filtered 164 conversations to 164 based on include/exclude rules
2025-05-26 11:10:34,399 - fetch_conversations - INFO - Found 164 total conversations, 164 to process after filtering
2025-05-26 11:10:34,399 - fetch_conversations - INFO - Processing conversation 1/164 (0.6%): general
2025-05-26 11:11:12,742 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:11:58,575 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:12:16,002 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:12:40,850 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:13:03,505 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:13:34,354 - mongo_handler - INFO - Batch stored 100 messages in general
2025-05-26 11:13:44,688 - mongo_handler - INFO - Batch stored 91 messages in general
2025-05-26 11:13:44,688 - fetch_conversations - INFO - Stored 811 messages for general
2025-05-26 11:13:44,688 - fetch_conversations - INFO - Processing conversation 2/164 (1.2%): random
2025-05-26 11:14:25,651 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:15:04,508 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:15:24,845 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:15:53,031 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:16:28,938 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:16:45,197 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:17:05,160 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:17:21,401 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:17:36,290 - mongo_handler - INFO - Batch stored 100 messages in random
2025-05-26 11:17:37,806 - mongo_handler - INFO - Batch stored 80 messages in random
2025-05-26 11:17:37,807 - fetch_conversations - INFO - Stored 1128 messages for random
2025-05-26 11:17:37,807 - fetch_conversations - INFO - Processing conversation 3/164 (1.8%): well-beat
2025-05-26 11:18:10,483 - mongo_handler - INFO - Batch stored 100 messages in well-beat
2025-05-26 11:18:50,993 - mongo_handler - INFO - Batch stored 100 messages in well-beat
2025-05-26 11:18:50,999 - mongo_handler - INFO - Batch stored 12 messages in well-beat
2025-05-26 11:18:50,999 - fetch_conversations - INFO - Stored 259 messages for well-beat
2025-05-26 11:18:50,999 - fetch_conversations - INFO - Processing conversation 4/164 (2.4%): innovation-team-รวม
2025-05-26 11:18:52,487 - mongo_handler - INFO - Batch stored 27 messages in innovation-team-รวม
2025-05-26 11:18:52,487 - fetch_conversations - INFO - Stored 27 messages for innovation-team-รวม
2025-05-26 11:18:52,487 - fetch_conversations - INFO - Processing conversation 5/164 (3.0%): 4-innovation-team
2025-05-26 11:19:30,251 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:19:41,407 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:19:51,822 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:11,606 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:26,371 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:39,924 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:51,952 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:56,614 - mongo_handler - INFO - Batch stored 100 messages in 4-innovation-team
2025-05-26 11:20:56,617 - mongo_handler - INFO - Batch stored 3 messages in 4-innovation-team
2025-05-26 11:20:56,617 - fetch_conversations - INFO - Stored 879 messages for 4-innovation-team
2025-05-26 11:20:56,617 - fetch_conversations - INFO - Processing conversation 6/164 (3.7%): squad_wellhealth
2025-05-26 11:22:35,926 - mongo_handler - INFO - Batch stored 100 messages in squad_wellhealth
2025-05-26 11:23:21,281 - mongo_handler - INFO - Batch stored 100 messages in squad_wellhealth
2025-05-26 11:23:31,518 - mongo_handler - INFO - Batch stored 100 messages in squad_wellhealth
2025-05-26 11:23:39,244 - mongo_handler - INFO - Batch stored 80 messages in squad_wellhealth
2025-05-26 11:23:39,246 - fetch_conversations - INFO - Stored 482 messages for squad_wellhealth
2025-05-26 11:23:39,246 - fetch_conversations - INFO - Processing conversation 7/164 (4.3%): well-screening-her-will
2025-05-26 11:23:57,119 - mongo_handler - INFO - Batch stored 55 messages in well-screening-her-will
2025-05-26 11:23:57,121 - fetch_conversations - INFO - Stored 66 messages for well-screening-her-will
2025-05-26 11:23:57,121 - fetch_conversations - INFO - Processing conversation 8/164 (4.9%): 9-algorithm-addwise
2025-05-26 11:25:20,172 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:26:32,556 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:27:37,920 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:28:13,069 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:28:51,413 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:30:14,607 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:30:26,460 - mongo_handler - INFO - Batch stored 100 messages in 9-algorithm-addwise
2025-05-26 11:30:39,092 - mongo_handler - INFO - Batch stored 91 messages in 9-algorithm-addwise
2025-05-26 11:30:39,093 - fetch_conversations - INFO - Stored 1043 messages for 9-algorithm-addwise
2025-05-26 11:30:39,093 - fetch_conversations - INFO - Processing conversation 9/164 (5.5%): get-health-project
2025-05-26 11:31:19,102 - mongo_handler - INFO - Batch stored 100 messages in get-health-project
2025-05-26 11:31:49,073 - mongo_handler - INFO - Batch stored 100 messages in get-health-project
2025-05-26 11:31:56,500 - mongo_handler - INFO - Batch stored 100 messages in get-health-project
2025-05-26 11:31:56,502 - mongo_handler - INFO - Batch stored 2 messages in get-health-project
2025-05-26 11:31:56,502 - fetch_conversations - INFO - Stored 350 messages for get-health-project
2025-05-26 11:31:56,502 - fetch_conversations - INFO - Processing conversation 10/164 (6.1%): thonburi-med-int
2025-05-26 11:32:09,914 - mongo_handler - INFO - Batch stored 100 messages in thonburi-med-int
2025-05-26 11:32:20,467 - mongo_handler - INFO - Batch stored 100 messages in thonburi-med-int
2025-05-26 11:32:27,799 - mongo_handler - INFO - Batch stored 100 messages in thonburi-med-int
2025-05-26 11:32:32,207 - mongo_handler - INFO - Batch stored 93 messages in thonburi-med-int
2025-05-26 11:32:32,207 - fetch_conversations - INFO - Stored 414 messages for thonburi-med-int
2025-05-26 11:32:32,207 - fetch_conversations - INFO - Processing conversation 11/164 (6.7%): invitrace-esports
2025-05-26 11:32:35,309 - mongo_handler - INFO - Batch stored 11 messages in invitrace-esports
2025-05-26 11:32:35,310 - fetch_conversations - INFO - Stored 12 messages for invitrace-esports
2025-05-26 11:32:35,310 - fetch_conversations - INFO - Processing conversation 12/164 (7.3%): param-9-hospital
2025-05-26 11:32:38,550 - mongo_handler - INFO - Batch stored 100 messages in param-9-hospital
2025-05-26 11:32:38,553 - mongo_handler - INFO - Batch stored 11 messages in param-9-hospital
2025-05-26 11:32:38,553 - fetch_conversations - INFO - Stored 112 messages for param-9-hospital
2025-05-26 11:32:38,553 - fetch_conversations - INFO - Processing conversation 13/164 (7.9%): i-live-well-project
2025-05-26 11:33:11,746 - mongo_handler - INFO - Batch stored 100 messages in i-live-well-project
2025-05-26 11:33:38,103 - mongo_handler - INFO - Batch stored 100 messages in i-live-well-project
2025-05-26 11:33:38,106 - mongo_handler - INFO - Batch stored 6 messages in i-live-well-project
2025-05-26 11:33:38,106 - fetch_conversations - INFO - Stored 243 messages for i-live-well-project
2025-05-26 11:33:38,106 - fetch_conversations - INFO - Processing conversation 14/164 (8.5%): ai-testing
2025-05-26 11:33:52,076 - mongo_handler - INFO - Batch stored 97 messages in ai-testing
2025-05-26 11:33:52,078 - fetch_conversations - INFO - Stored 105 messages for ai-testing
2025-05-26 11:33:52,078 - fetch_conversations - INFO - Processing conversation 15/164 (9.1%): phuonly
2025-05-26 11:33:53,551 - mongo_handler - INFO - Batch stored 7 messages in phuonly
2025-05-26 11:33:53,551 - fetch_conversations - INFO - Stored 7 messages for phuonly
2025-05-26 11:33:53,551 - fetch_conversations - INFO - Processing conversation 16/164 (9.8%): slh-product-design-team
2025-05-26 11:33:55,328 - mongo_handler - INFO - Batch stored 100 messages in slh-product-design-team
2025-05-26 11:33:55,330 - mongo_handler - INFO - Batch stored 10 messages in slh-product-design-team
2025-05-26 11:33:55,331 - fetch_conversations - INFO - Stored 110 messages for slh-product-design-team
2025-05-26 11:33:55,331 - fetch_conversations - INFO - Processing conversation 17/164 (10.4%): share-cv-profile-for-recruit
2025-05-26 11:34:06,028 - mongo_handler - INFO - Batch stored 62 messages in share-cv-profile-for-recruit
2025-05-26 11:34:06,029 - fetch_conversations - INFO - Stored 68 messages for share-cv-profile-for-recruit
2025-05-26 11:34:06,029 - fetch_conversations - INFO - Processing conversation 18/164 (11.0%): product-lead-initiative
2025-05-26 11:34:09,161 - mongo_handler - INFO - Batch stored 86 messages in product-lead-initiative
2025-05-26 11:34:09,162 - fetch_conversations - INFO - Stored 87 messages for product-lead-initiative
2025-05-26 11:34:09,162 - fetch_conversations - INFO - Processing conversation 19/164 (11.6%): consulting-g5
2025-05-26 11:34:21,164 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:32,276 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:43,018 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:49,456 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:51,022 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:54,199 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:57,185 - mongo_handler - INFO - Batch stored 100 messages in consulting-g5
2025-05-26 11:34:57,185 - fetch_conversations - INFO - Stored 723 messages for consulting-g5
2025-05-26 11:34:57,185 - fetch_conversations - INFO - Processing conversation 20/164 (12.2%): product-management
2025-05-26 11:34:58,631 - mongo_handler - INFO - Batch stored 7 messages in product-management
2025-05-26 11:34:58,631 - fetch_conversations - INFO - Stored 7 messages for product-management
2025-05-26 11:34:58,631 - fetch_conversations - INFO - Processing conversation 21/164 (12.8%): 5-head-of-operation
2025-05-26 11:35:12,703 - mongo_handler - INFO - Batch stored 100 messages in 5-head-of-operation
2025-05-26 11:35:12,707 - mongo_handler - INFO - Batch stored 15 messages in 5-head-of-operation
2025-05-26 11:35:12,707 - fetch_conversations - INFO - Stored 123 messages for 5-head-of-operation
2025-05-26 11:35:12,707 - fetch_conversations - INFO - Processing conversation 22/164 (13.4%): announcement
2025-05-26 11:35:19,013 - mongo_handler - INFO - Batch stored 59 messages in announcement
2025-05-26 11:35:19,014 - fetch_conversations - INFO - Stored 62 messages for announcement
2025-05-26 11:35:19,014 - fetch_conversations - INFO - Processing conversation 23/164 (14.0%): back-offices-archived-archived
2025-05-26 11:35:37,686 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:36:08,319 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:36:21,944 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:36:52,346 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:37:16,850 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:37:35,539 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:37:39,892 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:37:55,669 - mongo_handler - INFO - Batch stored 100 messages in back-offices-archived-archived
2025-05-26 11:38:12,326 - mongo_handler - INFO - Batch stored 89 messages in back-offices-archived-archived
2025-05-26 11:38:12,327 - fetch_conversations - INFO - Stored 997 messages for back-offices-archived-archived
2025-05-26 11:38:12,327 - fetch_conversations - INFO - Processing conversation 24/164 (14.6%): bd-po
2025-05-26 11:38:19,733 - mongo_handler - INFO - Batch stored 100 messages in bd-po
2025-05-26 11:38:39,661 - mongo_handler - INFO - Batch stored 100 messages in bd-po
2025-05-26 11:38:54,886 - mongo_handler - INFO - Batch stored 100 messages in bd-po
2025-05-26 11:39:06,536 - mongo_handler - INFO - Batch stored 99 messages in bd-po
2025-05-26 11:39:06,537 - fetch_conversations - INFO - Stored 431 messages for bd-po
2025-05-26 11:39:06,537 - fetch_conversations - INFO - Processing conversation 25/164 (15.2%): itele-mtl
2025-05-26 11:39:13,801 - mongo_handler - INFO - Batch stored 26 messages in itele-mtl
2025-05-26 11:39:13,801 - fetch_conversations - INFO - Stored 30 messages for itele-mtl
2025-05-26 11:39:13,801 - fetch_conversations - INFO - Processing conversation 26/164 (15.9%): get-health-uat
2025-05-26 11:43:09,238 - mongo_handler - INFO - Batch stored 100 messages in get-health-uat
2025-05-26 11:46:04,150 - mongo_handler - INFO - Batch stored 100 messages in get-health-uat
2025-05-26 11:49:14,320 - mongo_handler - INFO - Batch stored 100 messages in get-health-uat
2025-05-26 11:49:14,328 - mongo_handler - INFO - Batch stored 15 messages in get-health-uat
2025-05-26 11:49:14,328 - fetch_conversations - INFO - Stored 697 messages for get-health-uat
2025-05-26 11:49:14,328 - fetch_conversations - INFO - Processing conversation 27/164 (16.5%): 7-pm-project-managment
2025-05-26 11:49:17,193 - mongo_handler - INFO - Batch stored 19 messages in 7-pm-project-managment
2025-05-26 11:49:17,194 - fetch_conversations - INFO - Stored 20 messages for 7-pm-project-managment
2025-05-26 11:49:17,194 - fetch_conversations - INFO - Processing conversation 28/164 (17.1%): สั่งข้าวเรียกด้วย
2025-05-26 11:51:42,220 - mongo_handler - INFO - Batch stored 100 messages in ส_งข_าวเร_ยกด_วย
2025-05-26 11:52:18,944 - mongo_handler - INFO - Batch stored 54 messages in ส_งข_าวเร_ยกด_วย
2025-05-26 11:52:18,944 - fetch_conversations - INFO - Stored 260 messages for สั่งข้าวเรียกด้วย
2025-05-26 11:52:18,944 - fetch_conversations - INFO - Processing conversation 29/164 (17.7%): get-health
2025-05-26 11:52:20,362 - mongo_handler - INFO - Batch stored 2 messages in get-health
2025-05-26 11:52:20,362 - fetch_conversations - INFO - Stored 2 messages for get-health
2025-05-26 11:52:20,362 - fetch_conversations - INFO - Processing conversation 30/164 (18.3%): proj_telecare
2025-05-26 11:53:11,963 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 11:54:04,625 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 11:55:43,280 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 11:56:52,307 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 11:59:48,083 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 12:00:38,206 - mongo_handler - INFO - Batch stored 100 messages in proj_telecare
2025-05-26 12:00:51,580 - mongo_handler - INFO - Batch stored 25 messages in proj_telecare
2025-05-26 12:00:51,581 - fetch_conversations - INFO - Stored 956 messages for proj_telecare
2025-05-26 12:00:51,581 - fetch_conversations - INFO - Processing conversation 31/164 (18.9%): ds-gang
2025-05-26 12:01:36,787 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:02:18,320 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:02:18,347 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:02:23,086 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:02:25,984 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:03:01,929 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:04:00,269 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:04:49,151 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:05:23,468 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:05:48,453 - mongo_handler - INFO - Batch stored 100 messages in ds-gang
2025-05-26 12:06:02,040 - mongo_handler - INFO - Batch stored 32 messages in ds-gang
2025-05-26 12:06:02,041 - fetch_conversations - INFO - Stored 1232 messages for ds-gang
2025-05-26 12:06:02,041 - fetch_conversations - INFO - Processing conversation 32/164 (19.5%): proj_slh-uat
2025-05-26 12:06:57,401 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:07:17,337 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:07:51,258 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:08:14,272 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:09:09,374 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:10:32,265 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:13:28,510 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:14:30,543 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:15:27,892 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:17:24,220 - mongo_handler - INFO - Batch stored 100 messages in proj_slh-uat
2025-05-26 12:17:57,160 - mongo_handler - INFO - Batch stored 75 messages in proj_slh-uat
2025-05-26 12:17:57,160 - fetch_conversations - INFO - Stored 1537 messages for proj_slh-uat
2025-05-26 12:17:57,160 - fetch_conversations - INFO - Processing conversation 33/164 (20.1%): deep-listener
2025-05-26 12:18:00,293 - mongo_handler - INFO - Batch stored 35 messages in deep-listener
2025-05-26 12:18:00,294 - fetch_conversations - INFO - Stored 36 messages for deep-listener
2025-05-26 12:18:00,294 - fetch_conversations - INFO - Processing conversation 34/164 (20.7%): discuss-welllife-step-improvement
2025-05-26 12:18:09,553 - mongo_handler - INFO - Batch stored 62 messages in discuss-welllife-step-improvement
2025-05-26 12:18:09,554 - fetch_conversations - INFO - Stored 67 messages for discuss-welllife-step-improvement
2025-05-26 12:18:09,554 - fetch_conversations - INFO - Processing conversation 35/164 (21.3%): proj_ninnin
2025-05-26 12:21:44,112 - mongo_handler - INFO - Batch stored 100 messages in proj_ninnin
2025-05-26 12:23:20,510 - mongo_handler - INFO - Batch stored 77 messages in proj_ninnin
2025-05-26 12:23:20,513 - fetch_conversations - INFO - Stored 383 messages for proj_ninnin
2025-05-26 12:23:20,513 - fetch_conversations - INFO - Processing conversation 36/164 (22.0%): rachvipa-mri-project
2025-05-26 12:23:57,573 - mongo_handler - INFO - Batch stored 100 messages in rachvipa-mri-project
2025-05-26 12:24:48,567 - mongo_handler - INFO - Batch stored 85 messages in rachvipa-mri-project
2025-05-26 12:24:48,569 - fetch_conversations - INFO - Stored 243 messages for rachvipa-mri-project
2025-05-26 12:24:48,569 - fetch_conversations - INFO - Processing conversation 37/164 (22.6%): board-games
2025-05-26 12:24:51,525 - mongo_handler - INFO - Batch stored 37 messages in board-games
2025-05-26 12:24:51,525 - fetch_conversations - INFO - Stored 38 messages for board-games
2025-05-26 12:24:51,525 - fetch_conversations - INFO - Processing conversation 38/164 (23.2%): rmc-dev
2025-05-26 12:24:52,979 - mongo_handler - INFO - Batch stored 10 messages in rmc-dev
2025-05-26 12:24:52,979 - fetch_conversations - INFO - Stored 10 messages for rmc-dev
2025-05-26 12:24:52,979 - fetch_conversations - INFO - Processing conversation 39/164 (23.8%): well-dtm
2025-05-26 12:26:07,806 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:28:07,506 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:29:23,289 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:29:36,442 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:29:50,298 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:30:18,350 - mongo_handler - INFO - Batch stored 100 messages in well-dtm
2025-05-26 12:30:32,054 - mongo_handler - INFO - Batch stored 86 messages in well-dtm
2025-05-26 12:30:32,054 - fetch_conversations - INFO - Stored 908 messages for well-dtm
2025-05-26 12:30:32,054 - fetch_conversations - INFO - Processing conversation 40/164 (24.4%): rachvipa-mri-dev
2025-05-26 12:30:39,427 - mongo_handler - INFO - Batch stored 38 messages in rachvipa-mri-dev
2025-05-26 12:30:39,427 - fetch_conversations - INFO - Stored 42 messages for rachvipa-mri-dev
2025-05-26 12:30:39,427 - fetch_conversations - INFO - Processing conversation 41/164 (25.0%): well-life-toyota-uat
2025-05-26 12:32:34,557 - mongo_handler - INFO - Batch stored 100 messages in well-life-toyota-uat
2025-05-26 12:33:32,403 - mongo_handler - INFO - Batch stored 100 messages in well-life-toyota-uat
2025-05-26 12:35:35,058 - mongo_handler - INFO - Batch stored 100 messages in well-life-toyota-uat
2025-05-26 12:36:38,874 - mongo_handler - INFO - Batch stored 77 messages in well-life-toyota-uat
2025-05-26 12:36:38,875 - fetch_conversations - INFO - Stored 614 messages for well-life-toyota-uat
2025-05-26 12:36:38,875 - fetch_conversations - INFO - Processing conversation 42/164 (25.6%): proj_itele-udon
2025-05-26 12:37:26,970 - mongo_handler - INFO - Batch stored 100 messages in proj_itele-udon
2025-05-26 12:38:09,650 - mongo_handler - INFO - Batch stored 82 messages in proj_itele-udon
2025-05-26 12:38:09,651 - fetch_conversations - INFO - Stored 241 messages for proj_itele-udon
2025-05-26 12:38:09,651 - fetch_conversations - INFO - Processing conversation 43/164 (26.2%): bd-data-scientist
2025-05-26 12:38:14,303 - mongo_handler - INFO - Batch stored 53 messages in bd-data-scientist
2025-05-26 12:38:14,303 - fetch_conversations - INFO - Stored 55 messages for bd-data-scientist
2025-05-26 12:38:14,303 - fetch_conversations - INFO - Processing conversation 44/164 (26.8%): mdi-ds-team
2025-05-26 12:38:43,196 - mongo_handler - INFO - Batch stored 100 messages in mdi-ds-team
2025-05-26 12:39:26,868 - mongo_handler - INFO - Batch stored 100 messages in mdi-ds-team
2025-05-26 12:39:32,630 - mongo_handler - INFO - Batch stored 100 messages in mdi-ds-team
2025-05-26 12:39:42,047 - mongo_handler - INFO - Batch stored 100 messages in mdi-ds-team
2025-05-26 12:40:07,998 - mongo_handler - INFO - Batch stored 100 messages in mdi-ds-team
2025-05-26 12:40:09,410 - mongo_handler - INFO - Batch stored 31 messages in mdi-ds-team
2025-05-26 12:40:09,410 - fetch_conversations - INFO - Stored 603 messages for mdi-ds-team
2025-05-26 12:40:09,410 - fetch_conversations - INFO - Processing conversation 45/164 (27.4%): healthup-sit-log
2025-05-26 12:40:11,149 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:11,166 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:13,557 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:13,580 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:15,607 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:19,025 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:19,055 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:21,050 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:21,085 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:23,313 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:23,352 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:25,310 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:25,355 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:27,128 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:27,179 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:29,189 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:29,245 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:31,151 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:31,212 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:33,181 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:33,250 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:35,071 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:35,144 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:37,594 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:37,705 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:39,573 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:39,659 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:43,316 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:43,407 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:45,659 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:48,649 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:50,657 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:50,760 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:52,929 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:54,524 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:56,989 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:40:58,578 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:00,380 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:00,500 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:02,939 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:03,068 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:05,533 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:05,668 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:07,823 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:09,520 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:13,114 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:13,262 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:15,280 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:15,428 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:17,211 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:17,364 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:19,242 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:19,405 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:21,683 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:21,850 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:23,942 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:24,117 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:26,176 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:26,353 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:28,461 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:28,650 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:30,811 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:31,014 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:32,928 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:33,132 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:35,745 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:35,953 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:38,087 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:38,302 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:40,280 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:40,507 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:42,673 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:42,902 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:45,088 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:45,319 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:47,268 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:47,510 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:49,819 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:50,065 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:52,199 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:52,449 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:54,572 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:54,827 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:57,017 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:57,280 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:59,711 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:41:59,981 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:02,005 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:02,274 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:04,437 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:04,710 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:06,642 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:06,930 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:09,401 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:09,690 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:12,371 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:12,663 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:14,683 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:14,981 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:17,488 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:17,815 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:19,978 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:21,852 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:24,129 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:24,457 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:26,422 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:28,212 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:33,532 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:33,858 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:36,115 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:36,446 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:38,587 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:38,964 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:51,523 - mongo_handler - INFO - Batch stored 100 messages in healthup-sit-log
2025-05-26 12:42:57,855 - mongo_handler - INFO - Batch stored 82 messages in healthup-sit-log
2025-05-26 12:42:57,856 - fetch_conversations - INFO - Stored 11505 messages for healthup-sit-log
2025-05-26 12:42:57,856 - fetch_conversations - INFO - Processing conversation 46/164 (28.0%): healthup-dev-log
2025-05-26 12:43:00,040 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:00,056 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:02,665 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:02,686 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:04,973 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:04,999 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:07,180 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:07,213 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:09,427 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:09,463 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:11,237 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:11,277 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:13,470 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:13,516 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:15,356 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:15,408 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:17,189 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:17,245 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:19,435 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:19,497 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:21,734 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:21,806 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:24,025 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:24,099 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:26,343 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:26,423 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:28,251 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:28,336 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:30,145 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:30,235 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:32,040 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:32,137 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:34,424 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:34,526 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:36,377 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:36,489 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:38,740 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:38,853 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:41,151 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:41,270 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:43,104 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:43,231 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:45,530 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:45,660 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:47,633 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:47,770 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:50,023 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:50,170 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:52,454 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:52,607 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:54,536 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:54,696 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:56,526 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:56,692 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:58,588 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:43:58,758 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:00,641 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:00,813 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:02,717 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:02,897 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:04,863 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:05,050 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:08,093 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:08,289 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:10,213 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:10,422 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:12,310 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:12,525 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:14,875 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:15,087 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:17,134 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:17,347 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:19,229 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:19,448 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:21,475 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:21,701 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:23,674 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:23,910 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:25,874 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:26,154 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:28,591 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:28,835 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:31,204 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:31,453 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:33,425 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:33,682 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:35,709 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:35,980 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:37,985 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:38,265 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:40,897 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:41,175 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:43,266 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:43,548 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:45,545 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:45,828 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:47,817 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:48,113 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:50,079 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:50,377 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:52,396 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:52,700 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:55,147 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:55,459 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:57,502 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:57,821 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:44:59,907 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:00,233 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:02,695 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:03,025 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:05,069 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:05,404 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:07,897 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:08,254 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:10,276 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:10,622 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:12,712 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:13,068 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:15,533 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:15,895 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:18,025 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:18,397 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:20,739 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:21,115 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:23,502 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:23,879 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:25,988 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:26,377 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:28,592 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:28,979 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:31,507 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:31,900 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:34,017 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:34,416 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:36,540 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:36,941 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:39,043 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:39,457 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:42,240 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:42,653 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:45,311 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:45,727 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:47,876 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:48,338 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:50,604 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:51,036 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:53,253 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:53,691 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:55,908 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:56,356 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:58,541 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:45:58,990 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:01,060 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:01,558 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:03,703 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:04,175 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:06,576 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:07,046 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:09,253 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:09,725 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:11,897 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:12,377 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:14,732 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:15,236 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:17,529 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:18,019 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:20,253 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:20,762 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:23,055 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:23,574 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:25,965 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:26,477 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:29,122 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:29,661 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:32,366 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:32,901 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:35,674 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:36,217 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:38,481 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:39,019 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:41,344 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:41,924 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:44,227 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:44,782 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:47,085 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:47,646 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:50,358 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:50,959 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:53,638 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:54,224 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:56,558 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:57,145 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:46:59,921 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:00,559 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:02,868 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:03,470 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:05,872 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:06,470 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:09,190 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:09,825 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:12,180 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:12,803 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:15,211 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:15,832 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:18,231 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:18,860 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:21,206 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:21,847 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:24,242 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:24,892 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:27,665 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:28,318 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:30,727 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:31,379 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:33,733 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:34,385 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:36,847 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:37,510 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:40,166 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:40,832 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:43,684 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:44,351 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:46,832 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:47,502 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:49,934 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:50,619 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:53,433 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:54,133 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:56,556 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:57,253 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:47:59,674 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:00,421 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:02,850 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:03,560 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:06,106 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:06,810 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:09,604 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:10,344 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:12,856 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:13,583 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:16,434 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:17,187 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:19,693 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:20,427 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:23,351 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:24,108 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:27,011 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:27,758 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:30,379 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:31,144 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:33,613 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:34,380 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:36,943 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:37,711 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:40,313 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:41,111 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:44,094 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:44,869 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:47,445 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:48,231 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:50,781 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:51,577 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:54,095 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:54,896 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:57,985 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:48:58,808 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:02,043 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:02,845 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:05,429 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:06,258 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:09,331 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:10,164 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:12,818 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:13,673 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:16,436 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:17,279 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:19,839 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:20,690 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:23,516 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:24,363 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:26,939 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:27,786 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:30,441 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:31,305 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:34,001 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:34,887 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:37,585 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:38,506 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:41,126 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:41,995 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:44,766 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:45,699 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:48,312 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:49,253 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:52,390 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:53,303 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:56,019 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:56,927 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:49:59,536 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:00,475 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:03,189 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:04,171 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:07,495 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:08,491 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:11,170 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:12,161 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:15,267 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:16,268 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:19,003 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:19,997 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:22,737 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:23,700 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:26,944 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:27,967 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:30,700 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:31,659 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:34,626 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:35,716 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:38,949 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:40,000 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:42,737 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:43,812 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:46,588 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:47,770 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:50,609 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:51,622 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:54,390 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:55,497 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:58,299 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:50:59,395 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:02,218 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:03,313 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:06,137 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:07,257 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:10,201 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:11,312 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:14,223 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:15,342 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:18,163 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:19,324 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:22,655 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:23,791 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:26,590 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:27,705 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:30,993 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:32,134 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:34,994 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:36,125 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:39,048 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:40,208 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:43,142 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:44,246 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:47,100 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:48,205 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:50,984 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:52,101 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:55,000 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:56,127 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:51:59,257 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:00,388 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:03,809 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:04,945 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:07,840 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:08,986 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:11,906 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:13,090 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:16,400 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:17,607 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:20,536 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:21,741 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:24,799 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:25,981 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:28,935 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:30,099 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:33,116 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:34,287 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:37,199 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:38,370 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:41,266 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:42,469 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:45,270 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:46,450 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:49,345 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:50,524 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:53,486 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:54,706 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:58,083 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:52:59,274 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:02,745 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:04,008 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:07,383 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:08,590 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:12,097 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:13,325 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:16,293 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:17,535 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:20,542 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:21,779 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:24,773 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:26,016 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:29,458 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:30,740 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:34,156 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:35,444 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:38,579 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:39,849 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:42,814 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:44,106 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:47,114 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:48,394 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:51,473 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:52,748 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:55,791 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:53:57,083 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:00,687 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:02,039 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:05,098 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:06,381 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:09,412 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:10,728 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:13,727 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:15,061 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:18,523 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:19,843 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:23,162 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:24,505 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:27,627 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:28,968 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:32,203 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:33,521 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:37,048 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:38,467 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:41,605 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:42,948 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:46,275 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:47,623 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:50,806 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:52,169 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:55,311 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:56,671 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:54:59,789 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:01,233 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:04,719 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:06,169 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:09,345 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:10,744 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:14,429 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:15,853 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:18,957 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:20,365 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:23,520 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:24,931 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:28,166 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:29,643 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:33,220 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:34,668 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:37,849 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:39,280 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:42,434 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:43,883 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:47,439 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:48,937 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:52,214 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:53,688 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:56,933 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:55:58,439 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:01,726 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:03,184 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:06,887 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:08,420 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:12,121 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:13,593 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:16,867 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:18,362 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:21,735 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:23,272 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:26,504 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:28,104 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:31,751 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:33,283 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:36,605 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:38,137 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:41,831 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:43,365 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:47,013 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:48,543 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:51,750 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:53,301 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:56,564 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:56:58,112 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:01,945 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:03,483 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:06,815 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:08,379 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:11,647 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:13,202 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:16,583 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:18,170 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:21,490 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:23,118 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:26,503 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:28,062 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:31,828 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:33,427 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:36,754 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:38,330 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:41,817 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:43,423 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:46,691 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:48,299 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:51,820 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:53,515 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:56,704 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:57:58,321 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:02,232 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:03,843 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:07,213 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:08,830 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:12,157 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:13,818 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:17,161 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:18,839 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:22,440 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:24,088 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:27,398 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:29,035 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:32,409 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:34,067 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:37,447 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:39,137 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:42,618 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:44,330 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:48,126 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:49,808 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:53,530 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:55,227 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:58:58,893 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:00,626 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:04,198 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:05,929 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:09,406 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:11,181 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:14,817 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:16,556 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:20,122 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:21,932 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:25,824 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:27,629 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:31,316 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:33,070 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:36,648 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:38,407 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:41,908 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:43,645 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:47,414 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:49,181 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:52,639 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:54,442 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 12:59:58,640 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:00,432 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:04,068 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:05,877 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:09,398 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:11,218 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:15,306 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:17,159 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:21,299 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:23,107 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:26,660 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:28,481 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:32,092 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:33,947 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:38,112 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:40,018 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:43,694 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:45,564 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:49,380 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:51,283 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:55,064 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:00:56,941 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:00,580 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:02,431 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:06,439 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:08,399 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:12,458 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:14,312 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:18,005 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:19,898 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:23,617 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:25,514 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:29,089 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:31,039 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:34,803 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:36,747 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:40,567 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:42,514 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:46,337 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:48,283 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:52,036 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:54,005 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:01:58,137 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:00,110 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:03,899 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:05,886 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:10,074 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:12,028 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:16,307 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:18,399 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:22,182 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:24,140 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:27,815 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:29,767 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:33,591 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:35,564 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:39,489 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:41,498 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:45,185 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:47,160 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:51,563 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:53,562 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:57,495 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:02:59,499 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:03,351 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:05,396 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:09,160 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:11,202 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:14,839 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:16,816 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:20,750 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:22,747 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:26,498 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:28,571 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:32,226 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:34,258 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:38,433 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:40,485 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:44,254 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:46,336 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:50,201 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:52,244 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:56,592 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:03:58,655 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:02,478 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:04,553 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:08,352 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:10,378 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:14,557 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:16,635 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:20,805 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:22,898 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:27,184 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:29,258 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:33,483 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:35,572 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:39,373 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:41,476 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:45,284 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:47,384 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:51,523 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:53,636 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:04:57,897 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:05:00,057 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev-log
2025-05-26 13:05:32,129 - slack_sdk.web.base_client - ERROR - Failed to send a request to Slack API server: The read operation timed out
2025-05-26 13:05:32,131 - slack_client - ERROR - Unexpected error fetching history for C06K9FAUMFW: The read operation timed out
2025-05-26 13:05:32,131 - fetch_conversations - INFO - Stored 66200 messages for healthup-dev-log
2025-05-26 13:05:32,132 - fetch_conversations - INFO - Processing conversation 47/164 (28.7%): ilivewell-food-ai
2025-05-26 13:05:39,265 - slack_sdk.web.base_client - ERROR - Failed to send a request to Slack API server: IncompleteRead(59888 bytes read, 87973 more expected)
2025-05-26 13:05:39,267 - slack_client - ERROR - Unexpected error fetching history for C06UPL264GP: IncompleteRead(59888 bytes read, 87973 more expected)
2025-05-26 13:05:39,267 - fetch_conversations - INFO - Stored 0 messages for ilivewell-food-ai
2025-05-26 13:05:39,267 - fetch_conversations - INFO - Processing conversation 48/164 (29.3%): well-health-log-dev
2025-05-26 13:05:43,957 - mongo_handler - INFO - Batch stored 100 messages in well-health-log-dev
2025-05-26 13:05:43,972 - mongo_handler - INFO - Batch stored 100 messages in well-health-log-dev
2025-05-26 13:05:48,509 - mongo_handler - INFO - Batch stored 100 messages in well-health-log-dev
2025-05-26 13:05:48,536 - mongo_handler - INFO - Batch stored 100 messages in well-health-log-dev
2025-05-26 13:05:53,378 - slack_sdk.web.base_client - ERROR - Failed to send a request to Slack API server: IncompleteRead(68716 bytes read, 129305 more expected)
2025-05-26 13:05:53,378 - slack_client - ERROR - Unexpected error fetching history for C07361PEH9C: IncompleteRead(68716 bytes read, 129305 more expected)
2025-05-26 13:05:53,378 - fetch_conversations - INFO - Stored 400 messages for well-health-log-dev
2025-05-26 13:05:53,378 - fetch_conversations - INFO - Processing conversation 49/164 (29.9%): well-health-log-prod
2025-05-26 13:05:57,741 - slack_sdk.web.base_client - ERROR - Failed to send a request to Slack API server: IncompleteRead(68716 bytes read, 25373 more expected)
2025-05-26 13:05:57,742 - slack_client - ERROR - Unexpected error fetching history for C073CDF9D8S: IncompleteRead(68716 bytes read, 25373 more expected)
2025-05-26 13:05:57,742 - fetch_conversations - INFO - Stored 0 messages for well-health-log-prod
2025-05-26 13:05:57,742 - fetch_conversations - INFO - Processing conversation 50/164 (30.5%): screen-squad-รวยรวยรวย
2025-05-26 13:07:44,149 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:09:12,386 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:11:10,378 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:11:53,411 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:12:48,748 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:14:00,049 - mongo_handler - INFO - Batch stored 100 messages in screen-squad-รวยรวยรวย
2025-05-26 13:14:00,066 - mongo_handler - INFO - Batch stored 37 messages in screen-squad-รวยรวยรวย
2025-05-26 13:14:00,066 - fetch_conversations - INFO - Stored 950 messages for screen-squad-รวยรวยรวย
2025-05-26 13:14:00,066 - fetch_conversations - INFO - Processing conversation 51/164 (31.1%): team_digital-product-development
2025-05-26 13:14:37,539 - mongo_handler - INFO - Batch stored 100 messages in team_digital-product-development
2025-05-26 13:14:37,542 - mongo_handler - INFO - Batch stored 12 messages in team_digital-product-development
2025-05-26 13:14:37,542 - fetch_conversations - INFO - Stored 136 messages for team_digital-product-development
2025-05-26 13:14:37,542 - fetch_conversations - INFO - Processing conversation 52/164 (31.7%): proj_dhs
2025-05-26 13:15:44,227 - mongo_handler - INFO - Batch stored 100 messages in proj_dhs
2025-05-26 13:16:47,344 - mongo_handler - INFO - Batch stored 100 messages in proj_dhs
2025-05-26 13:17:46,788 - mongo_handler - INFO - Batch stored 100 messages in proj_dhs
2025-05-26 13:18:51,994 - mongo_handler - INFO - Batch stored 100 messages in proj_dhs
2025-05-26 13:19:03,971 - mongo_handler - INFO - Batch stored 35 messages in proj_dhs
2025-05-26 13:19:03,971 - fetch_conversations - INFO - Stored 611 messages for proj_dhs
2025-05-26 13:19:03,971 - fetch_conversations - INFO - Processing conversation 53/164 (32.3%): system_dhs
2025-05-26 13:19:06,159 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:06,176 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:08,121 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:08,145 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:10,527 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:10,555 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:12,358 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:12,391 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:14,362 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:14,400 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:16,372 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:16,415 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:18,397 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:18,446 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:20,173 - mongo_handler - INFO - Batch stored 100 messages in user_system_dhs
2025-05-26 13:19:20,193 - mongo_handler - INFO - Batch stored 33 messages in user_system_dhs
2025-05-26 13:19:20,193 - fetch_conversations - INFO - Stored 1533 messages for system_dhs
2025-05-26 13:19:20,193 - fetch_conversations - INFO - Processing conversation 54/164 (32.9%): system_addwise
2025-05-26 13:19:21,628 - mongo_handler - INFO - Batch stored 2 messages in user_system_addwise
2025-05-26 13:19:21,628 - fetch_conversations - INFO - Stored 2 messages for system_addwise
2025-05-26 13:19:21,628 - fetch_conversations - INFO - Processing conversation 55/164 (33.5%): proj_addwise
2025-05-26 13:19:24,558 - mongo_handler - INFO - Batch stored 12 messages in proj_addwise
2025-05-26 13:19:24,558 - fetch_conversations - INFO - Stored 13 messages for proj_addwise
2025-05-26 13:19:24,558 - fetch_conversations - INFO - Processing conversation 56/164 (34.1%): proj_dhs-qa
2025-05-26 13:19:26,024 - mongo_handler - INFO - Batch stored 5 messages in proj_dhs-qa
2025-05-26 13:19:26,025 - fetch_conversations - INFO - Stored 5 messages for proj_dhs-qa
2025-05-26 13:19:26,025 - fetch_conversations - INFO - Processing conversation 57/164 (34.8%): system_product
2025-05-26 13:19:28,051 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:28,074 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:30,061 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:30,082 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:32,450 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:32,477 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:34,451 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:34,484 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:36,455 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:36,494 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:38,476 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:38,520 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:40,508 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:40,560 - mongo_handler - INFO - Batch stored 100 messages in user_system_product
2025-05-26 13:19:42,765 - mongo_handler - INFO - Batch stored 76 messages in user_system_product
2025-05-26 13:19:42,766 - fetch_conversations - INFO - Stored 1476 messages for system_product
2025-05-26 13:19:42,766 - fetch_conversations - INFO - Processing conversation 58/164 (35.4%): proj_rmc
2025-05-26 13:20:49,161 - mongo_handler - INFO - Batch stored 94 messages in proj_rmc
2025-05-26 13:20:49,161 - fetch_conversations - INFO - Stored 137 messages for proj_rmc
2025-05-26 13:20:49,161 - fetch_conversations - INFO - Processing conversation 59/164 (36.0%): proj_rmc-design
2025-05-26 13:20:50,705 - mongo_handler - INFO - Batch stored 13 messages in proj_rmc-design
2025-05-26 13:20:50,705 - fetch_conversations - INFO - Stored 13 messages for proj_rmc-design
2025-05-26 13:20:50,705 - fetch_conversations - INFO - Processing conversation 60/164 (36.6%): proj_slh-design
2025-05-26 13:20:55,106 - mongo_handler - INFO - Batch stored 12 messages in proj_slh-design
2025-05-26 13:20:55,106 - fetch_conversations - INFO - Stored 14 messages for proj_slh-design
2025-05-26 13:20:55,106 - fetch_conversations - INFO - Processing conversation 61/164 (37.2%): proj_slh
2025-05-26 13:22:10,472 - mongo_handler - INFO - Batch stored 100 messages in proj_slh
2025-05-26 13:22:55,544 - mongo_handler - INFO - Batch stored 100 messages in proj_slh
2025-05-26 13:23:06,014 - mongo_handler - INFO - Batch stored 48 messages in proj_slh
2025-05-26 13:23:06,015 - fetch_conversations - INFO - Stored 333 messages for proj_slh
2025-05-26 13:23:06,015 - fetch_conversations - INFO - Processing conversation 62/164 (37.8%): proj-pricing-bi-and-packages
2025-05-26 13:23:09,312 - mongo_handler - INFO - Batch stored 29 messages in proj-pricing-bi-and-packages
2025-05-26 13:23:09,313 - fetch_conversations - INFO - Stored 30 messages for proj-pricing-bi-and-packages
2025-05-26 13:23:09,313 - fetch_conversations - INFO - Processing conversation 63/164 (38.4%): ilwsales-invitracexaji
2025-05-26 13:23:10,842 - mongo_handler - INFO - Batch stored 2 messages in ilwsales-invitracexaji
2025-05-26 13:23:10,842 - fetch_conversations - INFO - Stored 2 messages for ilwsales-invitracexaji
2025-05-26 13:23:10,842 - fetch_conversations - INFO - Processing conversation 64/164 (39.0%): bay-pentest
2025-05-26 13:23:12,331 - mongo_handler - INFO - Batch stored 1 messages in bay-pentest
2025-05-26 13:23:12,331 - fetch_conversations - INFO - Stored 1 messages for bay-pentest
2025-05-26 13:23:12,331 - fetch_conversations - INFO - Processing conversation 65/164 (39.6%): project_bdms_samui
2025-05-26 13:23:45,507 - mongo_handler - INFO - Batch stored 100 messages in project_bdms_samui
2025-05-26 13:23:49,889 - mongo_handler - INFO - Batch stored 26 messages in project_bdms_samui
2025-05-26 13:23:49,889 - fetch_conversations - INFO - Stored 150 messages for project_bdms_samui
2025-05-26 13:23:49,889 - fetch_conversations - INFO - Processing conversation 66/164 (40.2%): system_bdms_samui
2025-05-26 13:23:52,041 - mongo_handler - INFO - Batch stored 39 messages in user_system_bdms_samui
2025-05-26 13:23:52,041 - fetch_conversations - INFO - Stored 39 messages for system_bdms_samui
2025-05-26 13:23:52,042 - fetch_conversations - INFO - Processing conversation 67/164 (40.9%): dept_product
2025-05-26 13:24:10,234 - mongo_handler - INFO - Batch stored 94 messages in dept_product
2025-05-26 13:24:10,235 - fetch_conversations - INFO - Stored 105 messages for dept_product
2025-05-26 13:24:10,235 - fetch_conversations - INFO - Processing conversation 68/164 (41.5%): proj_healthup
2025-05-26 13:24:13,861 - mongo_handler - INFO - Batch stored 17 messages in proj_healthup
2025-05-26 13:24:13,861 - fetch_conversations - INFO - Stored 18 messages for proj_healthup
2025-05-26 13:24:13,861 - fetch_conversations - INFO - Processing conversation 69/164 (42.1%): proj_bplus_bdms
2025-05-26 13:25:06,366 - mongo_handler - INFO - Batch stored 67 messages in proj_bplus_bdms
2025-05-26 13:25:06,367 - fetch_conversations - INFO - Stored 100 messages for proj_bplus_bdms
2025-05-26 13:25:06,367 - fetch_conversations - INFO - Processing conversation 70/164 (42.7%): เปิดท้าย-invitrace
2025-05-26 13:25:07,805 - mongo_handler - INFO - Batch stored 5 messages in เป_ดท_าย-invitrace
2025-05-26 13:25:07,805 - fetch_conversations - INFO - Stored 5 messages for เปิดท้าย-invitrace
2025-05-26 13:25:07,805 - fetch_conversations - INFO - Processing conversation 71/164 (43.3%): design-welllife
2025-05-26 13:27:35,775 - mongo_handler - INFO - Batch stored 68 messages in design-welllife
2025-05-26 13:27:35,776 - fetch_conversations - INFO - Stored 165 messages for design-welllife
2025-05-26 13:27:35,777 - fetch_conversations - INFO - Processing conversation 72/164 (43.9%): predict_health
2025-05-26 13:27:54,317 - mongo_handler - INFO - Batch stored 98 messages in predict_health
2025-05-26 13:27:54,318 - fetch_conversations - INFO - Stored 109 messages for predict_health
2025-05-26 13:27:54,318 - fetch_conversations - INFO - Processing conversation 73/164 (44.5%): healthup-release
2025-05-26 13:29:37,219 - mongo_handler - INFO - Batch stored 100 messages in healthup-release
2025-05-26 13:30:50,183 - mongo_handler - INFO - Batch stored 62 messages in healthup-release
2025-05-26 13:30:50,183 - fetch_conversations - INFO - Stored 274 messages for healthup-release
2025-05-26 13:30:50,183 - fetch_conversations - INFO - Processing conversation 74/164 (45.1%): wellsport-
2025-05-26 13:30:56,363 - mongo_handler - INFO - Batch stored 36 messages in wellsport-
2025-05-26 13:30:56,364 - fetch_conversations - INFO - Stored 39 messages for wellsport-
2025-05-26 13:30:56,364 - fetch_conversations - INFO - Processing conversation 75/164 (45.7%): back-offices
2025-05-26 13:31:02,532 - mongo_handler - INFO - Batch stored 100 messages in back-offices
2025-05-26 13:31:02,535 - mongo_handler - INFO - Batch stored 13 messages in back-offices
2025-05-26 13:31:02,535 - fetch_conversations - INFO - Stored 116 messages for back-offices
2025-05-26 13:31:02,535 - fetch_conversations - INFO - Processing conversation 76/164 (46.3%): proj_cgh
2025-05-26 13:31:50,623 - mongo_handler - INFO - Batch stored 100 messages in proj_cgh
2025-05-26 13:31:50,625 - mongo_handler - INFO - Batch stored 8 messages in proj_cgh
2025-05-26 13:31:50,625 - fetch_conversations - INFO - Stored 137 messages for proj_cgh
2025-05-26 13:31:50,625 - fetch_conversations - INFO - Processing conversation 77/164 (47.0%): proj_bcare
2025-05-26 13:31:52,213 - mongo_handler - INFO - Batch stored 14 messages in proj_bcare
2025-05-26 13:31:52,213 - fetch_conversations - INFO - Stored 14 messages for proj_bcare
2025-05-26 13:31:52,213 - fetch_conversations - INFO - Processing conversation 78/164 (47.6%): mpdm-parin.ki--natdanai.in--teevisit.po-1
2025-05-26 13:31:53,709 - mongo_handler - INFO - Batch stored 2 messages in mpdm-parin_ki--natdanai_in--teevisit_po-1
2025-05-26 13:31:53,709 - fetch_conversations - INFO - Stored 2 messages for mpdm-parin.ki--natdanai.in--teevisit.po-1
2025-05-26 13:31:53,709 - fetch_conversations - INFO - Processing conversation 79/164 (48.2%): team_product-development
2025-05-26 13:32:01,159 - mongo_handler - INFO - Batch stored 41 messages in team_product-development
2025-05-26 13:32:01,159 - fetch_conversations - INFO - Stored 45 messages for team_product-development
2025-05-26 13:32:01,159 - fetch_conversations - INFO - Processing conversation 80/164 (48.8%): squad_wellhospital
2025-05-26 13:32:02,858 - mongo_handler - INFO - Batch stored 8 messages in squad_wellhospital
2025-05-26 13:32:02,859 - fetch_conversations - INFO - Stored 8 messages for squad_wellhospital
2025-05-26 13:32:02,859 - fetch_conversations - INFO - Processing conversation 81/164 (49.4%): addwise-noti
2025-05-26 13:32:04,405 - mongo_handler - INFO - Batch stored 5 messages in addwise-noti
2025-05-26 13:32:04,405 - fetch_conversations - INFO - Stored 5 messages for addwise-noti
2025-05-26 13:32:04,405 - fetch_conversations - INFO - Processing conversation 82/164 (50.0%): chatbot-test
2025-05-26 13:32:35,855 - mongo_handler - INFO - Batch stored 8 messages in chatbot-test
2025-05-26 13:32:35,856 - fetch_conversations - INFO - Stored 21 messages for chatbot-test
2025-05-26 13:32:35,856 - fetch_conversations - INFO - Processing conversation 83/164 (50.6%): rmc-monitor
2025-05-26 13:32:38,308 - mongo_handler - INFO - Batch stored 100 messages in rmc-monitor
2025-05-26 13:32:38,325 - mongo_handler - INFO - Batch stored 100 messages in rmc-monitor
2025-05-26 13:32:39,884 - mongo_handler - INFO - Batch stored 31 messages in rmc-monitor
2025-05-26 13:32:39,885 - fetch_conversations - INFO - Stored 231 messages for rmc-monitor
2025-05-26 13:32:39,885 - fetch_conversations - INFO - Processing conversation 84/164 (51.2%): dev-sm-support
2025-05-26 13:32:41,387 - mongo_handler - INFO - Batch stored 15 messages in dev-sm-support
2025-05-26 13:32:41,387 - fetch_conversations - INFO - Stored 15 messages for dev-sm-support
2025-05-26 13:32:41,387 - fetch_conversations - INFO - Processing conversation 85/164 (51.8%): itele-notification
2025-05-26 13:32:43,441 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:43,455 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:45,236 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:45,260 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:47,196 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:47,222 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:49,436 - mongo_handler - INFO - Batch stored 100 messages in itele-notification
2025-05-26 13:32:49,446 - mongo_handler - INFO - Batch stored 27 messages in itele-notification
2025-05-26 13:32:49,446 - fetch_conversations - INFO - Stored 727 messages for itele-notification
2025-05-26 13:32:49,446 - fetch_conversations - INFO - Processing conversation 86/164 (52.4%): testviewi
2025-05-26 13:32:51,232 - mongo_handler - INFO - Batch stored 1 messages in testviewi
2025-05-26 13:32:51,232 - fetch_conversations - INFO - Stored 1 messages for testviewi
2025-05-26 13:32:51,232 - fetch_conversations - INFO - Processing conversation 87/164 (53.0%): back-offices-archived
2025-05-26 13:32:53,217 - mongo_handler - INFO - Batch stored 95 messages in back-offices-archived
2025-05-26 13:32:53,217 - fetch_conversations - INFO - Stored 95 messages for back-offices-archived
2025-05-26 13:32:53,217 - fetch_conversations - INFO - Processing conversation 88/164 (53.7%): i-tele-dev
2025-05-26 13:33:41,150 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:34:30,812 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:36:43,024 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:38:30,627 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:39:32,502 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:40:28,231 - mongo_handler - INFO - Batch stored 100 messages in i-tele-dev
2025-05-26 13:40:34,437 - mongo_handler - INFO - Batch stored 25 messages in i-tele-dev
2025-05-26 13:40:34,437 - fetch_conversations - INFO - Stored 882 messages for i-tele-dev
2025-05-26 13:40:34,437 - fetch_conversations - INFO - Processing conversation 89/164 (54.3%): well-life-dev
2025-05-26 13:41:20,292 - mongo_handler - INFO - Batch stored 100 messages in well-life-dev
2025-05-26 13:42:59,371 - mongo_handler - INFO - Batch stored 100 messages in well-life-dev
2025-05-26 13:43:55,700 - mongo_handler - INFO - Batch stored 88 messages in well-life-dev
2025-05-26 13:43:55,701 - fetch_conversations - INFO - Stored 409 messages for well-life-dev
2025-05-26 13:43:55,701 - fetch_conversations - INFO - Processing conversation 90/164 (54.9%): well-algorithm-dev
2025-05-26 13:46:25,394 - mongo_handler - INFO - Batch stored 100 messages in well-algorithm-dev
2025-05-26 13:48:14,237 - mongo_handler - INFO - Batch stored 100 messages in well-algorithm-dev
2025-05-26 13:48:17,144 - mongo_handler - INFO - Batch stored 25 messages in well-algorithm-dev
2025-05-26 13:48:17,144 - fetch_conversations - INFO - Stored 376 messages for well-algorithm-dev
2025-05-26 13:48:17,144 - fetch_conversations - INFO - Processing conversation 91/164 (55.5%): aten-invitrace
2025-05-26 13:49:33,819 - mongo_handler - INFO - Batch stored 100 messages in aten-invitrace
2025-05-26 13:50:45,918 - mongo_handler - INFO - Batch stored 100 messages in aten-invitrace
2025-05-26 13:53:11,902 - mongo_handler - INFO - Batch stored 100 messages in aten-invitrace
2025-05-26 13:53:20,549 - mongo_handler - INFO - Batch stored 39 messages in aten-invitrace
2025-05-26 13:53:20,549 - fetch_conversations - INFO - Stored 529 messages for aten-invitrace
2025-05-26 13:53:20,549 - fetch_conversations - INFO - Processing conversation 92/164 (56.1%): well-health-dev
2025-05-26 13:53:57,114 - mongo_handler - INFO - Batch stored 100 messages in well-health-dev
2025-05-26 13:54:55,959 - mongo_handler - INFO - Batch stored 63 messages in well-health-dev
2025-05-26 13:54:55,959 - fetch_conversations - INFO - Stored 220 messages for well-health-dev
2025-05-26 13:54:55,960 - fetch_conversations - INFO - Processing conversation 93/164 (56.7%): saint-louis-dev
2025-05-26 13:55:54,841 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 13:56:38,165 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 13:58:02,314 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 13:59:55,452 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 14:01:05,728 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 14:01:23,774 - mongo_handler - INFO - Batch stored 100 messages in saint-louis-dev
2025-05-26 14:01:23,778 - mongo_handler - INFO - Batch stored 8 messages in saint-louis-dev
2025-05-26 14:01:23,778 - fetch_conversations - INFO - Stored 849 messages for saint-louis-dev
2025-05-26 14:01:23,778 - fetch_conversations - INFO - Processing conversation 94/164 (57.3%): healthup-dev
2025-05-26 14:03:08,226 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:04:38,761 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:05:52,672 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:06:58,861 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:07:55,608 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:08:35,843 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:09:32,979 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:10:13,837 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:10:33,446 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:11:03,273 - mongo_handler - INFO - Batch stored 100 messages in healthup-dev
2025-05-26 14:11:23,065 - mongo_handler - INFO - Batch stored 99 messages in healthup-dev
2025-05-26 14:11:23,066 - fetch_conversations - INFO - Stored 1468 messages for healthup-dev
2025-05-26 14:11:23,066 - fetch_conversations - INFO - Processing conversation 95/164 (57.9%): healthup-uat
2025-05-26 14:13:47,518 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:16:02,127 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:18:10,177 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:19:48,875 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:20:38,022 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:21:58,241 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:23:20,719 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:24:22,655 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:25:24,094 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:26:15,321 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:27:57,435 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:28:22,039 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:29:57,761 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:30:32,268 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:32:09,792 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:33:27,259 - mongo_handler - INFO - Batch stored 100 messages in healthup-uat
2025-05-26 14:34:23,135 - mongo_handler - INFO - Batch stored 67 messages in healthup-uat
2025-05-26 14:34:23,136 - fetch_conversations - INFO - Stored 2561 messages for healthup-uat
2025-05-26 14:34:23,136 - fetch_conversations - INFO - Processing conversation 96/164 (58.5%): well-life-toyota-dev
2025-05-26 14:35:47,524 - mongo_handler - INFO - Batch stored 100 messages in well-life-toyota-dev
2025-05-26 14:36:41,809 - mongo_handler - INFO - Batch stored 100 messages in well-life-toyota-dev
2025-05-26 14:37:45,565 - mongo_handler - INFO - Batch stored 89 messages in well-life-toyota-dev
2025-05-26 14:37:45,566 - fetch_conversations - INFO - Stored 420 messages for well-life-toyota-dev
2025-05-26 14:37:45,566 - fetch_conversations - INFO - Processing conversation 97/164 (59.1%): oceanlife-dev
2025-05-26 14:38:17,969 - mongo_handler - INFO - Batch stored 100 messages in oceanlife-dev
2025-05-26 14:38:31,089 - mongo_handler - INFO - Batch stored 80 messages in oceanlife-dev
2025-05-26 14:38:31,090 - fetch_conversations - INFO - Stored 208 messages for oceanlife-dev
2025-05-26 14:38:31,090 - fetch_conversations - INFO - Processing conversation 98/164 (59.8%): D08SXL0FYKA
2025-05-26 14:38:33,911 - slack_client - ERROR - Slack API error fetching history for D08SXL0FYKA: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:33,911 - fetch_conversations - INFO - Stored 0 messages for D08SXL0FYKA
2025-05-26 14:38:33,911 - fetch_conversations - INFO - Processing conversation 99/164 (60.4%): D08RBQU09C3
2025-05-26 14:38:36,725 - slack_client - ERROR - Slack API error fetching history for D08RBQU09C3: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:36,726 - fetch_conversations - INFO - Stored 0 messages for D08RBQU09C3
2025-05-26 14:38:36,726 - fetch_conversations - INFO - Processing conversation 100/164 (61.0%): D08LMUGBR7U
2025-05-26 14:38:39,553 - slack_client - ERROR - Slack API error fetching history for D08LMUGBR7U: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:39,553 - fetch_conversations - INFO - Stored 0 messages for D08LMUGBR7U
2025-05-26 14:38:39,554 - fetch_conversations - INFO - Processing conversation 101/164 (61.6%): D08HRQMB96F
2025-05-26 14:38:42,346 - slack_client - ERROR - Slack API error fetching history for D08HRQMB96F: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:42,346 - fetch_conversations - INFO - Stored 0 messages for D08HRQMB96F
2025-05-26 14:38:42,346 - fetch_conversations - INFO - Processing conversation 102/164 (62.2%): D08HRN9E1UP
2025-05-26 14:38:45,156 - slack_client - ERROR - Slack API error fetching history for D08HRN9E1UP: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:45,156 - fetch_conversations - INFO - Stored 0 messages for D08HRN9E1UP
2025-05-26 14:38:45,156 - fetch_conversations - INFO - Processing conversation 103/164 (62.8%): D08GCTLLKV3
2025-05-26 14:38:47,981 - slack_client - ERROR - Slack API error fetching history for D08GCTLLKV3: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:47,981 - fetch_conversations - INFO - Stored 0 messages for D08GCTLLKV3
2025-05-26 14:38:47,982 - fetch_conversations - INFO - Processing conversation 104/164 (63.4%): D08EXQJTXJ8
2025-05-26 14:38:50,905 - slack_client - ERROR - Slack API error fetching history for D08EXQJTXJ8: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:50,905 - fetch_conversations - INFO - Stored 0 messages for D08EXQJTXJ8
2025-05-26 14:38:50,905 - fetch_conversations - INFO - Processing conversation 105/164 (64.0%): D08EEA7FBB2
2025-05-26 14:38:53,705 - slack_client - ERROR - Slack API error fetching history for D08EEA7FBB2: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:53,706 - fetch_conversations - INFO - Stored 0 messages for D08EEA7FBB2
2025-05-26 14:38:53,706 - fetch_conversations - INFO - Processing conversation 106/164 (64.6%): D08BJCQ9AAH
2025-05-26 14:38:56,508 - slack_client - ERROR - Slack API error fetching history for D08BJCQ9AAH: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:56,508 - fetch_conversations - INFO - Stored 0 messages for D08BJCQ9AAH
2025-05-26 14:38:56,508 - fetch_conversations - INFO - Processing conversation 107/164 (65.2%): D08B8A8AX7G
2025-05-26 14:38:59,414 - slack_client - ERROR - Slack API error fetching history for D08B8A8AX7G: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:38:59,414 - fetch_conversations - INFO - Stored 0 messages for D08B8A8AX7G
2025-05-26 14:38:59,414 - fetch_conversations - INFO - Processing conversation 108/164 (65.9%): D08B6S7HC5V
2025-05-26 14:39:02,211 - slack_client - ERROR - Slack API error fetching history for D08B6S7HC5V: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:02,211 - fetch_conversations - INFO - Stored 0 messages for D08B6S7HC5V
2025-05-26 14:39:02,211 - fetch_conversations - INFO - Processing conversation 109/164 (66.5%): D08B5GJLC59
2025-05-26 14:39:05,070 - slack_client - ERROR - Slack API error fetching history for D08B5GJLC59: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:05,071 - fetch_conversations - INFO - Stored 0 messages for D08B5GJLC59
2025-05-26 14:39:05,072 - fetch_conversations - INFO - Processing conversation 110/164 (67.1%): D08AA4VAAPK
2025-05-26 14:39:07,895 - slack_client - ERROR - Slack API error fetching history for D08AA4VAAPK: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:07,895 - fetch_conversations - INFO - Stored 0 messages for D08AA4VAAPK
2025-05-26 14:39:07,895 - fetch_conversations - INFO - Processing conversation 111/164 (67.7%): D089ZK93G7R
2025-05-26 14:39:10,708 - slack_client - ERROR - Slack API error fetching history for D089ZK93G7R: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:10,709 - fetch_conversations - INFO - Stored 0 messages for D089ZK93G7R
2025-05-26 14:39:10,709 - fetch_conversations - INFO - Processing conversation 112/164 (68.3%): D089TCVDZ4L
2025-05-26 14:39:13,532 - slack_client - ERROR - Slack API error fetching history for D089TCVDZ4L: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:13,533 - fetch_conversations - INFO - Stored 0 messages for D089TCVDZ4L
2025-05-26 14:39:13,533 - fetch_conversations - INFO - Processing conversation 113/164 (68.9%): D089MHTTAUD
2025-05-26 14:39:16,368 - slack_client - ERROR - Slack API error fetching history for D089MHTTAUD: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:16,370 - fetch_conversations - INFO - Stored 0 messages for D089MHTTAUD
2025-05-26 14:39:16,370 - fetch_conversations - INFO - Processing conversation 114/164 (69.5%): D089HEEG1QW
2025-05-26 14:39:19,188 - slack_client - ERROR - Slack API error fetching history for D089HEEG1QW: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:19,188 - fetch_conversations - INFO - Stored 0 messages for D089HEEG1QW
2025-05-26 14:39:19,188 - fetch_conversations - INFO - Processing conversation 115/164 (70.1%): D0899H0HR2B
2025-05-26 14:39:21,981 - slack_client - ERROR - Slack API error fetching history for D0899H0HR2B: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:21,982 - fetch_conversations - INFO - Stored 0 messages for D0899H0HR2B
2025-05-26 14:39:21,982 - fetch_conversations - INFO - Processing conversation 116/164 (70.7%): D087ZPC2CF7
2025-05-26 14:39:24,770 - slack_client - ERROR - Slack API error fetching history for D087ZPC2CF7: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:24,771 - fetch_conversations - INFO - Stored 0 messages for D087ZPC2CF7
2025-05-26 14:39:24,771 - fetch_conversations - INFO - Processing conversation 117/164 (71.3%): D085DQYPJPL
2025-05-26 14:39:27,587 - slack_client - ERROR - Slack API error fetching history for D085DQYPJPL: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:27,588 - fetch_conversations - INFO - Stored 0 messages for D085DQYPJPL
2025-05-26 14:39:27,588 - fetch_conversations - INFO - Processing conversation 118/164 (72.0%): D085BNUEG84
2025-05-26 14:39:30,404 - slack_client - ERROR - Slack API error fetching history for D085BNUEG84: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:30,404 - fetch_conversations - INFO - Stored 0 messages for D085BNUEG84
2025-05-26 14:39:30,404 - fetch_conversations - INFO - Processing conversation 119/164 (72.6%): D084MNNSKPW
2025-05-26 14:39:33,324 - slack_client - ERROR - Slack API error fetching history for D084MNNSKPW: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:33,326 - fetch_conversations - INFO - Stored 0 messages for D084MNNSKPW
2025-05-26 14:39:33,326 - fetch_conversations - INFO - Processing conversation 120/164 (73.2%): D084LMT4Z6U
2025-05-26 14:39:36,122 - slack_client - ERROR - Slack API error fetching history for D084LMT4Z6U: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:36,123 - fetch_conversations - INFO - Stored 0 messages for D084LMT4Z6U
2025-05-26 14:39:36,123 - fetch_conversations - INFO - Processing conversation 121/164 (73.8%): D084485517X
2025-05-26 14:39:38,951 - slack_client - ERROR - Slack API error fetching history for D084485517X: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:38,951 - fetch_conversations - INFO - Stored 0 messages for D084485517X
2025-05-26 14:39:38,952 - fetch_conversations - INFO - Processing conversation 122/164 (74.4%): D083SHXHWBH
2025-05-26 14:39:41,734 - slack_client - ERROR - Slack API error fetching history for D083SHXHWBH: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:41,735 - fetch_conversations - INFO - Stored 0 messages for D083SHXHWBH
2025-05-26 14:39:41,735 - fetch_conversations - INFO - Processing conversation 123/164 (75.0%): D083S8NHXT9
2025-05-26 14:39:44,695 - slack_client - ERROR - Slack API error fetching history for D083S8NHXT9: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:44,696 - fetch_conversations - INFO - Stored 0 messages for D083S8NHXT9
2025-05-26 14:39:44,696 - fetch_conversations - INFO - Processing conversation 124/164 (75.6%): D082Y35J6F8
2025-05-26 14:39:47,526 - slack_client - ERROR - Slack API error fetching history for D082Y35J6F8: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:47,526 - fetch_conversations - INFO - Stored 0 messages for D082Y35J6F8
2025-05-26 14:39:47,526 - fetch_conversations - INFO - Processing conversation 125/164 (76.2%): D082XNRBB08
2025-05-26 14:39:50,311 - slack_client - ERROR - Slack API error fetching history for D082XNRBB08: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:50,312 - fetch_conversations - INFO - Stored 0 messages for D082XNRBB08
2025-05-26 14:39:50,312 - fetch_conversations - INFO - Processing conversation 126/164 (76.8%): D0826DM849X
2025-05-26 14:39:53,143 - slack_client - ERROR - Slack API error fetching history for D0826DM849X: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:53,144 - fetch_conversations - INFO - Stored 0 messages for D0826DM849X
2025-05-26 14:39:53,144 - fetch_conversations - INFO - Processing conversation 127/164 (77.4%): D081PSFBT8Q
2025-05-26 14:39:55,982 - slack_client - ERROR - Slack API error fetching history for D081PSFBT8Q: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:55,983 - fetch_conversations - INFO - Stored 0 messages for D081PSFBT8Q
2025-05-26 14:39:55,983 - fetch_conversations - INFO - Processing conversation 128/164 (78.0%): D080SF7LMSB
2025-05-26 14:39:58,809 - slack_client - ERROR - Slack API error fetching history for D080SF7LMSB: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:39:58,809 - fetch_conversations - INFO - Stored 0 messages for D080SF7LMSB
2025-05-26 14:39:58,809 - fetch_conversations - INFO - Processing conversation 129/164 (78.7%): D080M7L1ZV1
2025-05-26 14:40:01,593 - slack_client - ERROR - Slack API error fetching history for D080M7L1ZV1: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:01,593 - fetch_conversations - INFO - Stored 0 messages for D080M7L1ZV1
2025-05-26 14:40:01,593 - fetch_conversations - INFO - Processing conversation 130/164 (79.3%): D080FBLCESX
2025-05-26 14:40:04,421 - slack_client - ERROR - Slack API error fetching history for D080FBLCESX: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:04,422 - fetch_conversations - INFO - Stored 0 messages for D080FBLCESX
2025-05-26 14:40:04,422 - fetch_conversations - INFO - Processing conversation 131/164 (79.9%): D07V41MFKNY
2025-05-26 14:40:07,404 - slack_client - ERROR - Slack API error fetching history for D07V41MFKNY: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:07,404 - fetch_conversations - INFO - Stored 0 messages for D07V41MFKNY
2025-05-26 14:40:07,404 - fetch_conversations - INFO - Processing conversation 132/164 (80.5%): D07V410HHR7
2025-05-26 14:40:10,229 - slack_client - ERROR - Slack API error fetching history for D07V410HHR7: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:10,229 - fetch_conversations - INFO - Stored 0 messages for D07V410HHR7
2025-05-26 14:40:10,229 - fetch_conversations - INFO - Processing conversation 133/164 (81.1%): D07UQBW5LLT
2025-05-26 14:40:13,016 - slack_client - ERROR - Slack API error fetching history for D07UQBW5LLT: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:13,017 - fetch_conversations - INFO - Stored 0 messages for D07UQBW5LLT
2025-05-26 14:40:13,017 - fetch_conversations - INFO - Processing conversation 134/164 (81.7%): D07TZB8RRDX
2025-05-26 14:40:15,835 - slack_client - ERROR - Slack API error fetching history for D07TZB8RRDX: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:15,836 - fetch_conversations - INFO - Stored 0 messages for D07TZB8RRDX
2025-05-26 14:40:15,836 - fetch_conversations - INFO - Processing conversation 135/164 (82.3%): D07TU31578D
2025-05-26 14:40:18,644 - slack_client - ERROR - Slack API error fetching history for D07TU31578D: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:18,644 - fetch_conversations - INFO - Stored 0 messages for D07TU31578D
2025-05-26 14:40:18,645 - fetch_conversations - INFO - Processing conversation 136/164 (82.9%): D07TTKVTVQC
2025-05-26 14:40:21,452 - slack_client - ERROR - Slack API error fetching history for D07TTKVTVQC: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:21,453 - fetch_conversations - INFO - Stored 0 messages for D07TTKVTVQC
2025-05-26 14:40:21,453 - fetch_conversations - INFO - Processing conversation 137/164 (83.5%): D07SU75D7LM
2025-05-26 14:40:24,278 - slack_client - ERROR - Slack API error fetching history for D07SU75D7LM: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:24,278 - fetch_conversations - INFO - Stored 0 messages for D07SU75D7LM
2025-05-26 14:40:24,278 - fetch_conversations - INFO - Processing conversation 138/164 (84.1%): D07SM4X01DF
2025-05-26 14:40:27,112 - slack_client - ERROR - Slack API error fetching history for D07SM4X01DF: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:27,112 - fetch_conversations - INFO - Stored 0 messages for D07SM4X01DF
2025-05-26 14:40:27,112 - fetch_conversations - INFO - Processing conversation 139/164 (84.8%): D07SF2A2QC9
2025-05-26 14:40:29,980 - slack_client - ERROR - Slack API error fetching history for D07SF2A2QC9: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:29,980 - fetch_conversations - INFO - Stored 0 messages for D07SF2A2QC9
2025-05-26 14:40:29,980 - fetch_conversations - INFO - Processing conversation 140/164 (85.4%): D07SEJ2EHPT
2025-05-26 14:40:32,977 - slack_client - ERROR - Slack API error fetching history for D07SEJ2EHPT: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:32,978 - fetch_conversations - INFO - Stored 0 messages for D07SEJ2EHPT
2025-05-26 14:40:32,978 - fetch_conversations - INFO - Processing conversation 141/164 (86.0%): D07S4T4RQ68
2025-05-26 14:40:35,789 - slack_client - ERROR - Slack API error fetching history for D07S4T4RQ68: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:35,790 - fetch_conversations - INFO - Stored 0 messages for D07S4T4RQ68
2025-05-26 14:40:35,790 - fetch_conversations - INFO - Processing conversation 142/164 (86.6%): D07RQHC5URE
2025-05-26 14:40:38,631 - slack_client - ERROR - Slack API error fetching history for D07RQHC5URE: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:38,632 - fetch_conversations - INFO - Stored 0 messages for D07RQHC5URE
2025-05-26 14:40:38,632 - fetch_conversations - INFO - Processing conversation 143/164 (87.2%): D07RJ1DM8JU
2025-05-26 14:40:41,439 - slack_client - ERROR - Slack API error fetching history for D07RJ1DM8JU: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:41,439 - fetch_conversations - INFO - Stored 0 messages for D07RJ1DM8JU
2025-05-26 14:40:41,439 - fetch_conversations - INFO - Processing conversation 144/164 (87.8%): D07R8NJSB7Y
2025-05-26 14:40:44,251 - slack_client - ERROR - Slack API error fetching history for D07R8NJSB7Y: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:44,251 - fetch_conversations - INFO - Stored 0 messages for D07R8NJSB7Y
2025-05-26 14:40:44,252 - fetch_conversations - INFO - Processing conversation 145/164 (88.4%): D07R75WT37H
2025-05-26 14:40:47,124 - slack_client - ERROR - Slack API error fetching history for D07R75WT37H: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:47,124 - fetch_conversations - INFO - Stored 0 messages for D07R75WT37H
2025-05-26 14:40:47,124 - fetch_conversations - INFO - Processing conversation 146/164 (89.0%): D07R3LAP65S
2025-05-26 14:40:49,943 - slack_client - ERROR - Slack API error fetching history for D07R3LAP65S: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:49,944 - fetch_conversations - INFO - Stored 0 messages for D07R3LAP65S
2025-05-26 14:40:49,944 - fetch_conversations - INFO - Processing conversation 147/164 (89.6%): D07R1EE62FK
2025-05-26 14:40:52,739 - slack_client - ERROR - Slack API error fetching history for D07R1EE62FK: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:52,739 - fetch_conversations - INFO - Stored 0 messages for D07R1EE62FK
2025-05-26 14:40:52,739 - fetch_conversations - INFO - Processing conversation 148/164 (90.2%): D07QVE9BBC2
2025-05-26 14:40:55,600 - slack_client - ERROR - Slack API error fetching history for D07QVE9BBC2: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:55,600 - fetch_conversations - INFO - Stored 0 messages for D07QVE9BBC2
2025-05-26 14:40:55,600 - fetch_conversations - INFO - Processing conversation 149/164 (90.9%): D07QQMKCRPW
2025-05-26 14:40:58,430 - slack_client - ERROR - Slack API error fetching history for D07QQMKCRPW: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:40:58,431 - fetch_conversations - INFO - Stored 0 messages for D07QQMKCRPW
2025-05-26 14:40:58,431 - fetch_conversations - INFO - Processing conversation 150/164 (91.5%): D07QNGPEDFV
2025-05-26 14:41:01,249 - slack_client - ERROR - Slack API error fetching history for D07QNGPEDFV: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:01,250 - fetch_conversations - INFO - Stored 0 messages for D07QNGPEDFV
2025-05-26 14:41:01,250 - fetch_conversations - INFO - Processing conversation 151/164 (92.1%): D07QLBJPZNZ
2025-05-26 14:41:04,042 - slack_client - ERROR - Slack API error fetching history for D07QLBJPZNZ: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:04,042 - fetch_conversations - INFO - Stored 0 messages for D07QLBJPZNZ
2025-05-26 14:41:04,042 - fetch_conversations - INFO - Processing conversation 152/164 (92.7%): D07QCUPE98R
2025-05-26 14:41:06,876 - slack_client - ERROR - Slack API error fetching history for D07QCUPE98R: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:06,877 - fetch_conversations - INFO - Stored 0 messages for D07QCUPE98R
2025-05-26 14:41:06,877 - fetch_conversations - INFO - Processing conversation 153/164 (93.3%): D07Q7UH3RTR
2025-05-26 14:41:09,693 - slack_client - ERROR - Slack API error fetching history for D07Q7UH3RTR: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:09,693 - fetch_conversations - INFO - Stored 0 messages for D07Q7UH3RTR
2025-05-26 14:41:09,693 - fetch_conversations - INFO - Processing conversation 154/164 (93.9%): D07Q5SE62RF
2025-05-26 14:41:12,478 - slack_client - ERROR - Slack API error fetching history for D07Q5SE62RF: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:12,478 - fetch_conversations - INFO - Stored 0 messages for D07Q5SE62RF
2025-05-26 14:41:12,478 - fetch_conversations - INFO - Processing conversation 155/164 (94.5%): D07Q5BJ6GEM
2025-05-26 14:41:15,283 - slack_client - ERROR - Slack API error fetching history for D07Q5BJ6GEM: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:15,284 - fetch_conversations - INFO - Stored 0 messages for D07Q5BJ6GEM
2025-05-26 14:41:15,284 - fetch_conversations - INFO - Processing conversation 156/164 (95.1%): D07PV5TQS1L
2025-05-26 14:41:18,105 - slack_client - ERROR - Slack API error fetching history for D07PV5TQS1L: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:18,105 - fetch_conversations - INFO - Stored 0 messages for D07PV5TQS1L
2025-05-26 14:41:18,105 - fetch_conversations - INFO - Processing conversation 157/164 (95.7%): D07PUD23RJS
2025-05-26 14:41:20,925 - slack_client - ERROR - Slack API error fetching history for D07PUD23RJS: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:20,927 - fetch_conversations - INFO - Stored 0 messages for D07PUD23RJS
2025-05-26 14:41:20,927 - fetch_conversations - INFO - Processing conversation 158/164 (96.3%): D07PT688A6P
2025-05-26 14:41:23,742 - slack_client - ERROR - Slack API error fetching history for D07PT688A6P: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:23,743 - fetch_conversations - INFO - Stored 0 messages for D07PT688A6P
2025-05-26 14:41:23,743 - fetch_conversations - INFO - Processing conversation 159/164 (97.0%): D07PSTT9T0W
2025-05-26 14:41:26,599 - slack_client - ERROR - Slack API error fetching history for D07PSTT9T0W: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:26,599 - fetch_conversations - INFO - Stored 0 messages for D07PSTT9T0W
2025-05-26 14:41:26,599 - fetch_conversations - INFO - Processing conversation 160/164 (97.6%): D07PSSGC8NP
2025-05-26 14:41:29,390 - slack_client - ERROR - Slack API error fetching history for D07PSSGC8NP: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:29,391 - fetch_conversations - INFO - Stored 0 messages for D07PSSGC8NP
2025-05-26 14:41:29,391 - fetch_conversations - INFO - Processing conversation 161/164 (98.2%): D07PSLVP23U
2025-05-26 14:41:32,256 - slack_client - ERROR - Slack API error fetching history for D07PSLVP23U: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:32,256 - fetch_conversations - INFO - Stored 0 messages for D07PSLVP23U
2025-05-26 14:41:32,256 - fetch_conversations - INFO - Processing conversation 162/164 (98.8%): D07PPQZRD9B
2025-05-26 14:41:35,130 - slack_client - ERROR - Slack API error fetching history for D07PPQZRD9B: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:35,131 - fetch_conversations - INFO - Stored 0 messages for D07PPQZRD9B
2025-05-26 14:41:35,131 - fetch_conversations - INFO - Processing conversation 163/164 (99.4%): D07PCU9LK6K
2025-05-26 14:41:37,927 - slack_client - ERROR - Slack API error fetching history for D07PCU9LK6K: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:37,927 - fetch_conversations - INFO - Stored 0 messages for D07PCU9LK6K
2025-05-26 14:41:37,927 - fetch_conversations - INFO - Processing conversation 164/164 (100.0%): D07PC4TNQ1M
2025-05-26 14:41:40,805 - slack_client - ERROR - Slack API error fetching history for D07PC4TNQ1M: The request to the Slack API failed. (url: https://www.slack.com/api/conversations.history)
The server responded with: {'ok': False, 'error': 'missing_scope', 'needed': 'im:history', 'provided': 'identify,channels:history,groups:history,mpim:history,channels:read,groups:read,im:read,mpim:read,team:read,users:read,users.profile:read'}
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Stored 0 messages for D07PC4TNQ1M
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Conversation fetch process completed
2025-05-26 14:41:40,805 - fetch_conversations - INFO - === FINAL STATISTICS ===
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Conversations processed: 164
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Messages stored: 110224
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Threads processed: 6854
2025-05-26 14:41:40,805 - fetch_conversations - INFO - Errors encountered: 0
2025-05-26 14:41:40,808 - fetch_conversations - INFO - Total collections created: 100
2025-05-26 14:41:40,821 - fetch_conversations - INFO - Collection 'well-screening-her-will': 66 messages
2025-05-26 14:41:40,836 - fetch_conversations - INFO - Collection 'general': 811 messages
2025-05-26 14:41:40,841 - fetch_conversations - INFO - Collection 'rachvipa-mri-project': 243 messages
2025-05-26 14:41:40,844 - fetch_conversations - INFO - Collection 'squad_wellhospital': 8 messages
2025-05-26 14:41:40,863 - fetch_conversations - INFO - Collection 'proj_slh-uat': 1537 messages
2025-05-26 14:41:40,863 - __main__ - INFO - Step 6: Export completed successfully!
2025-05-26 14:41:40,906 - mongo_handler - INFO - MongoDB connection closed
2025-05-26 14:41:40,906 - __main__ - INFO - === Slack Chat History Exporter Completed ===
