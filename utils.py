"""
Utility functions for Slack Chat History Exporter
"""
import re
import time
import logging
from typing import List, Dict, Any
from functools import wraps

logger = logging.getLogger(__name__)


def sanitize_collection_name(name: str) -> str:
    """
    Sanitize a string to be used as a MongoDB collection name.
    
    MongoDB collection names have restrictions:
    - Cannot contain certain characters like $, spaces, etc.
    - Cannot start with system. prefix
    - Must be valid UTF-8
    """
    # Replace invalid characters with underscores
    sanitized = re.sub(r'[^\w\-]', '_', name)
    
    # Remove consecutive underscores
    sanitized = re.sub(r'_+', '_', sanitized)
    
    # Remove leading/trailing underscores
    sanitized = sanitized.strip('_')
    
    # Ensure it doesn't start with 'system.'
    if sanitized.startswith('system_'):
        sanitized = 'user_' + sanitized
    
    # Ensure it's not empty
    if not sanitized:
        sanitized = 'unnamed_conversation'
    
    # MongoDB collection names are limited to 120 characters
    if len(sanitized) > 120:
        sanitized = sanitized[:120]
    
    return sanitized


def create_dm_collection_name(participants: List[Dict[str, Any]]) -> str:
    """
    Create a collection name for a direct message conversation.
    
    Args:
        participants: List of user objects with display_name, real_name, or name
    
    Returns:
        Sanitized collection name for the DM
    """
    names = []
    for participant in participants:
        # Try to get the best available name
        name = (participant.get('display_name') or 
                participant.get('real_name') or 
                participant.get('name') or 
                participant.get('id', 'unknown'))
        names.append(name)
    
    # Sort names for consistency
    names.sort()
    
    # Create collection name
    if len(names) == 2:
        collection_name = f"dm_{names[0]}_{names[1]}"
    else:
        collection_name = f"group_dm_{'_'.join(names)}"
    
    return sanitize_collection_name(collection_name)


def create_mpim_collection_name(name: str, participants: List[Dict[str, Any]]) -> str:
    """
    Create a collection name for a multi-party instant message (group DM).
    
    Args:
        name: The MPIM name from Slack
        participants: List of user objects
    
    Returns:
        Sanitized collection name for the MPIM
    """
    if name and name != 'mpdm-':
        # Use the provided name if it's meaningful
        return sanitize_collection_name(f"mpim_{name}")
    else:
        # Fall back to participant-based naming
        return create_dm_collection_name(participants)


def retry_on_rate_limit(max_retries: int = 3, base_delay: float = 1.0):
    """
    Decorator to retry API calls on rate limit errors.
    
    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Base delay between retries (exponential backoff)
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    
                    # Check if it's a rate limit error
                    if hasattr(e, 'response') and hasattr(e.response, 'status_code'):
                        if e.response.status_code == 429:  # Too Many Requests
                            if attempt < max_retries:
                                delay = base_delay * (2 ** attempt)  # Exponential backoff
                                logger.warning(f"Rate limited. Retrying in {delay} seconds... (attempt {attempt + 1}/{max_retries})")
                                time.sleep(delay)
                                continue
                    
                    # For other errors, don't retry
                    if attempt < max_retries:
                        delay = base_delay
                        logger.warning(f"API call failed: {str(e)}. Retrying in {delay} seconds... (attempt {attempt + 1}/{max_retries})")
                        time.sleep(delay)
                    else:
                        logger.error(f"API call failed after {max_retries} retries: {str(e)}")
                        raise last_exception
            
            raise last_exception
        return wrapper
    return decorator


def setup_logging(log_level: str = 'INFO', log_file: str = None) -> logging.Logger:
    """
    Set up logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR)
        log_file: Optional log file path
    
    Returns:
        Configured logger
    """
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Set up root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler (if specified)
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    return root_logger


def format_timestamp(ts: str) -> str:
    """
    Format Slack timestamp for better readability.
    
    Args:
        ts: Slack timestamp string
    
    Returns:
        Formatted timestamp
    """
    try:
        timestamp = float(ts)
        return time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))
    except (ValueError, TypeError):
        return ts


def estimate_progress(current: int, total: int) -> str:
    """
    Create a simple progress indicator.
    
    Args:
        current: Current progress count
        total: Total count
    
    Returns:
        Progress string
    """
    if total == 0:
        return "0%"
    
    percentage = (current / total) * 100
    return f"{current}/{total} ({percentage:.1f}%)"
