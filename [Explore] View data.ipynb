import pandas as pd

df_members = pd.read_json('export_data/slack_chat_history.members.json')
df_users = pd.read_json('export_data/slack_chat_history.users_list.json')


print(df_members.shape)
df_members.head()

print(df_users.shape)
df_users.head()

df_conversations = pd.read_json('export_data/slack_chat_history.conversations_list.json')
print(df_conversations.shape)
# df_conversations.head()
df_conversations[df_conversations.duplicated()]

'D08SXL0FYKA' in df_users._id.values

