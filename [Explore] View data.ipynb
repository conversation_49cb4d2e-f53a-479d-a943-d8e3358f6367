{"cells": [{"cell_type": "code", "execution_count": 14, "id": "631a47d3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df_members = pd.read_json('export_data/slack_chat_history.members.json')\n", "df_users = pd.read_json('export_data/slack_chat_history.users_list.json')\n"]}, {"cell_type": "code", "execution_count": 15, "id": "d3dfe5f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(121, 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>display_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>jatnipat</td>\n", "      <td>jatnipat</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON><PERSON></td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  _id        display_name\n", "0          Risa Arisa          Risa Arisa\n", "1  May<PERSON><PERSON> Sarunchana  MayHyun Sa<PERSON>ana\n", "2                                        \n", "3            jatnipat            jatnipat\n", "4       Mee Teerapong       Mee Teerapong"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["print(df_members.shape)\n", "df_members.head()"]}, {"cell_type": "code", "execution_count": 16, "id": "b6e82595", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(259, 2)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>_id</th>\n", "      <th>display_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>U0767C6PT2A</td>\n", "      <td>Ploi Prompron</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>U076SM73BL1</td>\n", "      <td><PERSON><PERSON></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>U089JGQQTEE</td>\n", "      <td>Karn<PERSON><PERSON> (NAM)</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>U0895HM0FQE</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>U055Q3F8N4V</td>\n", "      <td>9.​<PERSON><PERSON><PERSON></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["           _id    display_name\n", "0  U0767C6PT2A   Ploi Prompron\n", "1  U076SM73BL1  Krane Teevisit\n", "2  U089JGQQTEE  Karntida (NAM)\n", "3  U0895HM0FQE                \n", "4  U055Q3F8N4V     9.​<PERSON><PERSON><PERSON>"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["print(df_users.shape)\n", "df_users.head()"]}, {"cell_type": "code", "execution_count": 13, "id": "0c35637d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(164, 1)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>98</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>100</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>101</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>102</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>159</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>160</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>161</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>162</th>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>163</th>\n", "      <td>None</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>66 rows × 1 columns</p>\n", "</div>"], "text/plain": ["     name\n", "98   None\n", "99   None\n", "100  None\n", "101  None\n", "102  None\n", "..    ...\n", "159  None\n", "160  None\n", "161  None\n", "162  None\n", "163  None\n", "\n", "[66 rows x 1 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["df_conversations = pd.read_json('export_data/slack_chat_history.conversations_list.json')\n", "print(df_conversations.shape)\n", "# df_conversations.head()\n", "df_conversations[df_conversations.duplicated()]"]}, {"cell_type": "code", "execution_count": 18, "id": "9217939f", "metadata": {}, "outputs": [{"data": {"text/plain": ["False"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["'D08SXL0FYKA' in df_users._id.values"]}, {"cell_type": "code", "execution_count": null, "id": "aebb624e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "addwise", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 5}