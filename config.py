"""
Configuration settings for Slack Chat History Exporter
"""
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Slack Configuration
SLACK_USER_TOKEN = os.getenv('SLACK_USER_TOKEN')
if not SLACK_USER_TOKEN:
    raise ValueError("SLACK_USER_TOKEN environment variable is required")

# MongoDB Configuration
MONGODB_CONNECTION_STRING = os.getenv('MONGODB_CONNECTION_STRING', 'mongodb://localhost:27017/')
MONGODB_DATABASE_NAME = os.getenv('MONGODB_DATABASE_NAME', 'slack_chat_history')

# API Rate Limiting
RATE_LIMIT_DELAY = float(os.getenv('RATE_LIMIT_DELAY', '1.0'))  # seconds between API calls
MAX_RETRIES = int(os.getenv('MAX_RETRIES', '3'))

# Conversation Fetching Settings
CONVERSATIONS_LIMIT = int(os.getenv('CONVERSATIONS_LIMIT', '200'))  # per API call
MESSAGES_LIMIT = int(os.getenv('MESSAGES_LIMIT', '200'))  # per API call
INCLUDE_ARCHIVED = os.getenv('INCLUDE_ARCHIVED', 'false').lower() == 'true'

# Conversation Types to Include
CONVERSATION_TYPES = os.getenv('CONVERSATION_TYPES', 'public_channel,private_channel,mpim,im').split(',')

# Logging Configuration
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FILE = os.getenv('LOG_FILE', 'slack_export.log')
