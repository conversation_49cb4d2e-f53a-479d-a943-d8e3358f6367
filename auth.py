"""
Authentication module for Slack Chat History Exporter
"""
import logging
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from config import SLACK_USER_TOKEN

logger = logging.getLogger(__name__)


class SlackAuth:
    """
    Handles Slack authentication and token validation.
    """
    
    def __init__(self, token: str = None):
        """
        Initialize Slack authentication.
        
        Args:
            token: Slack User OAuth Token. If not provided, uses config.SLACK_USER_TOKEN
        """
        self.token = token or SLACK_USER_TOKEN
        if not self.token:
            raise ValueError("Slack token is required")
        
        self.client = None
        self.user_info = None
        self.team_info = None
    
    def authenticate(self) -> WebClient:
        """
        Authenticate with <PERSON>lack and validate the token.
        
        Returns:
            Authenticated WebClient instance
        
        Raises:
            SlackApiError: If authentication fails
            ValueError: If token is invalid
        """
        try:
            # Create WebClient instance
            self.client = WebClient(token=self.token)
            
            # Test authentication
            logger.info("Testing Slack authentication...")
            auth_response = self.client.auth_test()
            
            if not auth_response["ok"]:
                raise ValueError(f"Authentication failed: {auth_response.get('error', 'Unknown error')}")
            
            # Store user and team information
            self.user_info = {
                'user_id': auth_response['user_id'],
                'user': auth_response['user'],
                'team_id': auth_response['team_id'],
                'team': auth_response['team']
            }
            
            logger.info(f"Successfully authenticated as {self.user_info['user']} on team {self.user_info['team']}")
            
            # Get detailed team information
            try:
                team_response = self.client.team_info()
                if team_response["ok"]:
                    self.team_info = team_response["team"]
                    logger.info(f"Team: {self.team_info.get('name', 'Unknown')} ({self.team_info.get('domain', 'Unknown domain')})")
            except SlackApiError as e:
                logger.warning(f"Could not fetch team info: {e}")
            
            return self.client
            
        except SlackApiError as e:
            error_msg = f"Slack API error during authentication: {e.response['error']}"
            logger.error(error_msg)
            raise
        except Exception as e:
            error_msg = f"Unexpected error during authentication: {str(e)}"
            logger.error(error_msg)
            raise
    
    def validate_scopes(self) -> dict:
        """
        Validate that the token has the required scopes for conversation history access.
        
        Returns:
            Dictionary with scope validation results
        """
        required_scopes = [
            'channels:history',
            'channels:read',
            'groups:history',
            'groups:read',
            'im:history',
            'im:read',
            'mpim:history',
            'mpim:read',
            'users:read'
        ]
        
        try:
            # Get token info to check scopes
            auth_response = self.client.auth_test()
            
            # Note: auth.test doesn't return scopes for user tokens
            # We'll try to make test calls to validate permissions
            validation_results = {}
            
            logger.info("Validating token permissions...")
            
            # Test conversations.list access
            try:
                test_response = self.client.conversations_list(limit=1)
                validation_results['conversations_access'] = test_response["ok"]
                logger.info("✓ Conversations access validated")
            except SlackApiError as e:
                validation_results['conversations_access'] = False
                logger.warning(f"✗ Conversations access failed: {e.response['error']}")
            
            # Test users.list access
            try:
                test_response = self.client.users_list(limit=1)
                validation_results['users_access'] = test_response["ok"]
                logger.info("✓ Users access validated")
            except SlackApiError as e:
                validation_results['users_access'] = False
                logger.warning(f"✗ Users access failed: {e.response['error']}")
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Error validating scopes: {str(e)}")
            return {'error': str(e)}
    
    def get_user_info(self) -> dict:
        """
        Get authenticated user information.
        
        Returns:
            User information dictionary
        """
        return self.user_info
    
    def get_team_info(self) -> dict:
        """
        Get team information.
        
        Returns:
            Team information dictionary
        """
        return self.team_info
    
    def get_client(self) -> WebClient:
        """
        Get the authenticated WebClient instance.
        
        Returns:
            WebClient instance
        
        Raises:
            RuntimeError: If not authenticated yet
        """
        if not self.client:
            raise RuntimeError("Not authenticated. Call authenticate() first.")
        return self.client


def create_authenticated_client(token: str = None) -> tuple[WebClient, dict]:
    """
    Convenience function to create and authenticate a Slack client.
    
    Args:
        token: Optional Slack token. Uses config.SLACK_USER_TOKEN if not provided.
    
    Returns:
        Tuple of (WebClient, user_info)
    
    Raises:
        SlackApiError: If authentication fails
        ValueError: If token is invalid
    """
    auth = SlackAuth(token)
    client = auth.authenticate()
    
    # Validate scopes
    scope_validation = auth.validate_scopes()
    if not scope_validation.get('conversations_access', False):
        logger.warning("Token may not have sufficient permissions for conversation access")
    
    return client, auth.get_user_info()
