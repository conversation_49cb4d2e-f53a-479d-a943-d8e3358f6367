# Slack Configuration
# Get your User OAuth Token from https://api.slack.com/apps
# Required scopes: channels:history, channels:read, groups:history, groups:read, im:history, im:read, mpim:history, mpim:read, users:read
SLACK_USER_TOKEN=xoxp-your-user-oauth-token-here

# MongoDB Configuration
MONGODB_CONNECTION_STRING=mongodb://localhost:27017/
MONGODB_DATABASE_NAME=slack_chat_history

# Optional: Rate Limiting (adjust if you hit rate limits)
RATE_LIMIT_DELAY=1.0
MAX_RETRIES=3

# Optional: Conversation Fetching Settings
CONVERSATIONS_LIMIT=200
MESSAGES_LIMIT=200
INCLUDE_ARCHIVED=false
CONVERSATION_TYPES=public_channel,private_channel,mpim,im

# Optional: Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=slack_export.log
