"""
Main conversation fetching orchestrator
"""
import logging
from typing import Dict, Any, List
from slack_client import SlackConversationClient
from mongo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils import (
    sanitize_collection_name, create_dm_collection_name,
    create_mpim_collection_name, estimate_progress
)
from config import (
    SKIP_EXISTING_CONVERSATIONS, INCLUDE_CONVERSATIONS, EXCLUDE_CONVERSATIONS,
    STORE_USERS_LIST, STORE_CONVERSATIONS_LIST
)

logger = logging.getLogger(__name__)


class ConversationFetcher:
    """
    Orchestrates the fetching of all conversation types and their messages.
    """

    def __init__(self, slack_client: SlackConversationClient, mongo_handler: <PERSON><PERSON><PERSON><PERSON><PERSON>):
        """
        Initialize the conversation fetcher.

        Args:
            slack_client: Authenticated Slack client
            mongo_handler: MongoDB handler
        """
        self.slack_client = slack_client
        self.mongo_handler = mongo_handler
        self.stats = {
            'conversations_processed': 0,
            'messages_stored': 0,
            'threads_processed': 0,
            'errors': 0
        }

    def fetch_all_conversations(self) -> Dict[str, Any]:
        """
        Fetch all conversations and their messages.

        Returns:
            Dictionary with processing statistics
        """
        logger.info("Starting conversation fetch process...")

        # Step 1: Store users list if enabled
        if STORE_USERS_LIST:
            self._store_users_list()

        # Step 2: Collect all conversations and store conversations list if enabled
        conversations = list(self.slack_client.get_conversations())
        if STORE_CONVERSATIONS_LIST:
            self._store_conversations_list(conversations)

        # Step 3: Filter conversations based on configuration
        filtered_conversations = self._filter_conversations(conversations)
        total_conversations = len(filtered_conversations)

        logger.info(f"Found {len(conversations)} total conversations, {total_conversations} to process after filtering")

        if total_conversations == 0:
            logger.warning("No conversations to process after filtering. Check configuration and token permissions.")
            return self.stats

        # Step 4: Process each conversation
        for i, conversation in enumerate(filtered_conversations, 1):
            try:
                progress = estimate_progress(i, total_conversations)
                conversation_name = conversation.get('name', conversation['id'])
                logger.info(f"Processing conversation {progress}: {conversation_name}")

                # Check if we should skip existing conversations
                if SKIP_EXISTING_CONVERSATIONS:
                    collection_name = self._determine_collection_name(conversation)
                    if self.mongo_handler.collection_exists(collection_name):
                        logger.info(f"Skipping existing conversation: {conversation_name} (collection: {collection_name})")
                        self.stats['conversations_processed'] += 1
                        continue

                self._process_conversation(conversation)
                self.stats['conversations_processed'] += 1

            except Exception as e:
                logger.error(f"Error processing conversation {conversation['id']}: {str(e)}")
                self.stats['errors'] += 1

        logger.info("Conversation fetch process completed")
        self._log_final_stats()
        return self.stats

    def _process_conversation(self, conversation: Dict[str, Any]):
        """
        Process a single conversation.

        Args:
            conversation: Conversation object from Slack API
        """
        conversation_id = conversation['id']
        collection_name = self._determine_collection_name(conversation)

        logger.debug(f"Processing conversation {conversation_id} -> collection '{collection_name}'")

        # Store conversation metadata
        self.mongo_handler.store_conversation_metadata(conversation, collection_name)

        # Fetch and store messages
        messages_stored = self._fetch_and_store_messages(conversation_id, collection_name)
        self.stats['messages_stored'] += messages_stored

        logger.info(f"Stored {messages_stored} messages for {conversation.get('name', conversation_id)}")

    def _determine_collection_name(self, conversation: Dict[str, Any]) -> str:
        """
        Determine the appropriate collection name for a conversation.

        Args:
            conversation: Conversation object

        Returns:
            Sanitized collection name
        """
        if conversation.get('is_channel') or conversation.get('is_group'):
            # Public or private channel
            name = conversation.get('name', f"channel_{conversation['id']}")
            return sanitize_collection_name(name)

        elif conversation.get('is_mpim'):
            # Multi-party instant message
            name = conversation.get('name', '')
            members = self._get_conversation_participants(conversation)
            return create_mpim_collection_name(name, members)

        elif conversation.get('is_im'):
            # Direct message
            members = self._get_conversation_participants(conversation)
            return create_dm_collection_name(members)

        else:
            # Fallback
            name = conversation.get('name', f"unknown_{conversation['id']}")
            return sanitize_collection_name(name)

    def _get_conversation_participants(self, conversation: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Get participant information for a conversation.

        Args:
            conversation: Conversation object

        Returns:
            List of participant user objects
        """
        participants = []

        # For DMs, get the other user
        if conversation.get('is_im'):
            user_id = conversation.get('user')
            if user_id:
                user_info = self.slack_client.get_user_info(user_id)
                if user_info:
                    participants.append(user_info)

        # For MPIMs and other group conversations, get members
        else:
            member_ids = self.slack_client.get_conversation_members(conversation['id'])
            for user_id in member_ids[:10]:  # Limit to first 10 for naming
                user_info = self.slack_client.get_user_info(user_id)
                if user_info:
                    participants.append(user_info)

        return participants

    def _fetch_and_store_messages(self, conversation_id: str, collection_name: str) -> int:
        """
        Fetch and store all messages for a conversation.

        Args:
            conversation_id: Slack conversation ID
            collection_name: MongoDB collection name

        Returns:
            Number of messages stored
        """
        messages_stored = 0
        messages_batch = []
        batch_size = 100

        try:
            for message in self.slack_client.get_conversation_history(conversation_id):
                # Check if message has thread replies
                thread_replies = None
                if message.get('reply_count', 0) > 0 and message.get('thread_ts'):
                    thread_replies = self.slack_client.get_thread_replies(
                        conversation_id, message['thread_ts']
                    )
                    self.stats['threads_processed'] += 1
                    logger.debug(f"Fetched {len(thread_replies)} replies for thread {message['thread_ts']}")

                # Store message with thread replies
                if thread_replies:
                    # Store threaded message individually to include replies
                    success = self.mongo_handler.store_message(
                        message, collection_name, thread_replies
                    )
                    if success:
                        messages_stored += 1
                else:
                    # Add to batch for non-threaded messages
                    messages_batch.append(message)

                # Process batch when it reaches batch_size
                if len(messages_batch) >= batch_size:
                    batch_stored = self.mongo_handler.store_messages_batch(
                        messages_batch, collection_name
                    )
                    messages_stored += batch_stored
                    messages_batch = []

            # Process remaining messages in batch
            if messages_batch:
                batch_stored = self.mongo_handler.store_messages_batch(
                    messages_batch, collection_name
                )
                messages_stored += batch_stored

        except Exception as e:
            logger.error(f"Error fetching messages for {conversation_id}: {str(e)}")
            self.stats['errors'] += 1

        return messages_stored

    def _log_final_stats(self):
        """Log final processing statistics."""
        logger.info("=== FINAL STATISTICS ===")
        logger.info(f"Conversations processed: {self.stats['conversations_processed']}")
        logger.info(f"Messages stored: {self.stats['messages_stored']}")
        logger.info(f"Threads processed: {self.stats['threads_processed']}")
        logger.info(f"Errors encountered: {self.stats['errors']}")

        # Get database statistics
        try:
            if self.mongo_handler.database is not None:
                collections = self.mongo_handler.database.list_collection_names()
                logger.info(f"Total collections created: {len(collections)}")

                # Log some collection stats
                for collection_name in collections[:5]:  # Show stats for first 5 collections
                    if not collection_name.startswith('_'):  # Skip metadata collections
                        stats = self.mongo_handler.get_conversation_stats(collection_name)
                        if stats:
                            logger.info(f"Collection '{collection_name}': {stats['total_messages']} messages")
        except Exception as e:
            logger.warning(f"Could not get database statistics: {str(e)}")

    def _filter_conversations(self, conversations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Filter conversations based on include/exclude configuration.

        Args:
            conversations: List of all conversations

        Returns:
            Filtered list of conversations
        """
        filtered = []

        for conversation in conversations:
            conversation_name = conversation.get('name', '')
            conversation_id = conversation.get('id', '')

            # For DMs and MPIMs, use ID as name for filtering since they don't have meaningful names
            filter_name = conversation_name if conversation_name else conversation_id

            # Check include list (if specified, only include conversations in this list)
            if INCLUDE_CONVERSATIONS is not None:
                if filter_name not in INCLUDE_CONVERSATIONS and conversation_id not in INCLUDE_CONVERSATIONS:
                    logger.debug(f"Excluding conversation '{filter_name}' - not in include list")
                    continue

            # Check exclude list (if specified, exclude conversations in this list)
            if EXCLUDE_CONVERSATIONS is not None:
                if filter_name in EXCLUDE_CONVERSATIONS or conversation_id in EXCLUDE_CONVERSATIONS:
                    logger.info(f"Excluding conversation '{filter_name}' - in exclude list")
                    continue

            filtered.append(conversation)

        logger.info(f"Filtered {len(conversations)} conversations to {len(filtered)} based on include/exclude rules")
        return filtered

    def _store_users_list(self):
        """Store all users in the users_list collection."""
        try:
            logger.info("Fetching and storing users list...")
            users = list(self.slack_client.get_all_users())

            if users:
                stored_count = self.mongo_handler.store_users_list(users)
                logger.info(f"Stored {stored_count} users in users_list collection")
                self.stats['users_stored'] = stored_count
            else:
                logger.warning("No users found to store")

        except Exception as e:
            logger.error(f"Error storing users list: {str(e)}")
            self.stats['errors'] += 1

    def _store_conversations_list(self, conversations: List[Dict[str, Any]]):
        """
        Store all conversations in the conversations_list collection.

        Args:
            conversations: List of conversation objects
        """
        try:
            logger.info("Storing conversations list...")

            if conversations:
                stored_count = self.mongo_handler.store_conversations_list(conversations)
                logger.info(f"Stored {stored_count} conversations in conversations_list collection")
                self.stats['conversations_list_stored'] = stored_count
            else:
                logger.warning("No conversations found to store")

        except Exception as e:
            logger.error(f"Error storing conversations list: {str(e)}")
            self.stats['errors'] += 1

    def get_stats(self) -> Dict[str, Any]:
        """
        Get current processing statistics.

        Returns:
            Statistics dictionary
        """
        return self.stats.copy()
