#!/usr/bin/env python3
"""
Test script to verify the bulk write fix for MongoDB operations.
"""
from mongo_handler import <PERSON>goH<PERSON><PERSON>
from utils import setup_logging

def test_bulk_write():
    """Test the fixed bulk write operations."""
    setup_logging('INFO')
    
    print("Testing MongoDB bulk write fix...")
    
    # Sample message data similar to what Slack API returns
    test_messages = [
        {
            'ts': '1726461960.462409',
            'type': 'message',
            'user': 'U06RG35SFA5',
            'text': 'เยอะมาก',  # Thai text that was in the error
            'thread_ts': None,
            'parent_user_id': None,
            'reply_count': 0,
            'blocks': [
                {
                    'type': 'rich_text',
                    'block_id': '5Uobj',
                    'elements': [
                        {
                            'type': 'rich_text_section',
                            'elements': [
                                {
                                    'type': 'text',
                                    'text': 'เยอะมาก'
                                }
                            ]
                        }
                    ]
                }
            ],
            'attachments': [],
            'files': [],
            'reactions': [
                {
                    'name': 'cat-urgent',
                    'users': ['U076E0UJEJE'],
                    'count': 1
                }
            ],
            'edited': None,
            'bot_id': None,
            'subtype': None,
            'hidden': False,
            'is_starred': False
        },
        {
            'ts': '1726461961.123456',
            'type': 'message',
            'user': '*********',
            'text': 'Test message 2',
            'reply_count': 0,
            'blocks': [],
            'attachments': [],
            'files': [],
            'reactions': [],
            'hidden': False,
            'is_starred': False
        }
    ]
    
    try:
        with MongoHandler() as mongo_handler:
            print("✅ Connected to MongoDB")
            
            # Test the batch store method
            collection_name = 'test_bulk_write'
            result = mongo_handler.store_messages_batch(test_messages, collection_name)
            
            print(f"✅ Bulk write successful! Stored {result} messages")
            
            # Verify the data was stored
            collection = mongo_handler.get_collection(collection_name)
            stored_count = collection.count_documents({})
            print(f"✅ Verification: {stored_count} messages found in collection")
            
            # Test with some sample data
            sample_message = collection.find_one({'ts': '1726461960.462409'})
            if sample_message:
                print(f"✅ Sample message retrieved: {sample_message['text']}")
            else:
                print("❌ Could not retrieve sample message")
            
            # Clean up test data
            collection.delete_many({})
            print("✅ Test data cleaned up")
            
            return True
            
    except Exception as e:
        print(f"❌ Test failed: {str(e)}")
        return False

def test_edge_cases():
    """Test edge cases for bulk write."""
    print("\nTesting edge cases...")
    
    try:
        with MongoHandler() as mongo_handler:
            # Test with empty list
            result = mongo_handler.store_messages_batch([], 'test_empty')
            print(f"✅ Empty list test: returned {result}")
            
            # Test with message missing timestamp
            invalid_messages = [
                {
                    'type': 'message',
                    'user': '*********',
                    'text': 'Message without timestamp'
                }
            ]
            result = mongo_handler.store_messages_batch(invalid_messages, 'test_invalid')
            print(f"✅ Invalid message test: returned {result}")
            
            return True
            
    except Exception as e:
        print(f"❌ Edge case test failed: {str(e)}")
        return False

if __name__ == "__main__":
    print("="*60)
    print("MONGODB BULK WRITE TEST")
    print("="*60)
    
    success1 = test_bulk_write()
    success2 = test_edge_cases()
    
    print("\n" + "="*60)
    if success1 and success2:
        print("🎉 All tests passed! Bulk write fix is working correctly.")
    else:
        print("❌ Some tests failed. Check the error messages above.")
    print("="*60)
