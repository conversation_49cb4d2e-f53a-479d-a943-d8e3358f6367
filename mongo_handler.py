"""
MongoDB handler for Slack Chat History Exporter
"""
import logging
from typing import List, Dict, Any, Optional
from pymongo import MongoClient, errors
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.operations import ReplaceOne
from config import MONGODB_CONNECTION_STRING, MONGODB_DATABASE_NAME

logger = logging.getLogger(__name__)


class MongoHandler:
    """
    Handles MongoDB connection and operations for storing Slack conversation history.
    """

    def __init__(self, connection_string: str = None, database_name: str = None):
        """
        Initialize MongoDB handler.

        Args:
            connection_string: MongoDB connection string
            database_name: Database name to use
        """
        self.connection_string = connection_string or MONG<PERSON>B_CONNECTION_STRING
        self.database_name = database_name or MONGODB_DATABASE_NAME
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None

    def connect(self) -> Database:
        """
        Connect to MongoDB and return database instance.

        Returns:
            MongoDB database instance

        Raises:
            ConnectionError: If connection fails
        """
        try:
            logger.info(f"Connecting to MongoDB: {self.connection_string}")
            self.client = MongoClient(self.connection_string, serverSelectionTimeoutMS=5000)

            # Test connection
            self.client.admin.command('ping')
            logger.info("Successfully connected to MongoDB")

            # Get database
            self.database = self.client[self.database_name]
            logger.info(f"Using database: {self.database_name}")

            return self.database

        except errors.ServerSelectionTimeoutError as e:
            error_msg = f"Failed to connect to MongoDB: {str(e)}"
            logger.error(error_msg)
            raise ConnectionError(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error connecting to MongoDB: {str(e)}"
            logger.error(error_msg)
            raise

    def get_collection(self, collection_name: str) -> Collection:
        """
        Get or create a collection.

        Args:
            collection_name: Name of the collection

        Returns:
            MongoDB collection instance

        Raises:
            RuntimeError: If not connected to database
        """
        if self.database is None:
            raise RuntimeError("Not connected to database. Call connect() first.")

        return self.database[collection_name]

    def store_conversation_metadata(self, conversation: Dict[str, Any], collection_name: str) -> bool:
        """
        Store conversation metadata in a special metadata collection.

        Args:
            conversation: Conversation object from Slack API
            collection_name: Collection name used for messages

        Returns:
            True if successful, False otherwise
        """
        try:
            metadata_collection = self.get_collection('_conversation_metadata')

            metadata_doc = {
                'conversation_id': conversation['id'],
                'collection_name': collection_name,
                'conversation_type': self._determine_conversation_type(conversation),
                'name': conversation.get('name', ''),
                'is_channel': conversation.get('is_channel', False),
                'is_group': conversation.get('is_group', False),
                'is_im': conversation.get('is_im', False),
                'is_mpim': conversation.get('is_mpim', False),
                'is_private': conversation.get('is_private', False),
                'is_archived': conversation.get('is_archived', False),
                'created': conversation.get('created'),
                'creator': conversation.get('creator'),
                'topic': conversation.get('topic', {}),
                'purpose': conversation.get('purpose', {}),
                'num_members': conversation.get('num_members'),
                'members': conversation.get('members', []),
                'last_updated': conversation.get('updated')
            }

            # Upsert the metadata
            metadata_collection.replace_one(
                {'conversation_id': conversation['id']},
                metadata_doc,
                upsert=True
            )

            logger.debug(f"Stored metadata for conversation {conversation['id']}")
            return True

        except Exception as e:
            logger.error(f"Error storing conversation metadata: {str(e)}")
            return False

    def store_message(self, message: Dict[str, Any], collection_name: str,
                     thread_replies: List[Dict[str, Any]] = None) -> bool:
        """
        Store a message with optional thread replies.

        Args:
            message: Message object from Slack API
            collection_name: Collection name to store in
            thread_replies: Optional list of thread reply messages

        Returns:
            True if successful, False otherwise
        """
        try:
            collection = self.get_collection(collection_name)

            # Prepare message document
            message_doc = {
                'ts': message['ts'],
                'type': message.get('type', 'message'),
                'user': message.get('user'),
                'text': message.get('text', ''),
                'thread_ts': message.get('thread_ts'),
                'parent_user_id': message.get('parent_user_id'),
                'reply_count': message.get('reply_count', 0),
                'reply_users_count': message.get('reply_users_count', 0),
                'latest_reply': message.get('latest_reply'),
                'subscribed': message.get('subscribed'),
                'last_read': message.get('last_read'),
                'unread_count': message.get('unread_count', 0),
                'blocks': message.get('blocks', []),
                'attachments': message.get('attachments', []),
                'files': message.get('files', []),
                'reactions': message.get('reactions', []),
                'edited': message.get('edited'),
                'bot_id': message.get('bot_id'),
                'app_id': message.get('app_id'),
                'subtype': message.get('subtype'),
                'hidden': message.get('hidden', False),
                'deleted_ts': message.get('deleted_ts'),
                'is_starred': message.get('is_starred', False)
            }

            # Add thread replies if provided
            if thread_replies:
                message_doc['thread_replies'] = thread_replies
                logger.debug(f"Message {message['ts']} has {len(thread_replies)} thread replies")

            # Use upsert to avoid duplicates
            collection.replace_one(
                {'ts': message['ts']},
                message_doc,
                upsert=True
            )

            return True

        except Exception as e:
            logger.error(f"Error storing message {message.get('ts', 'unknown')}: {str(e)}")
            return False

    def store_messages_batch(self, messages: List[Dict[str, Any]], collection_name: str) -> int:
        """
        Store multiple messages in batch for better performance.

        Args:
            messages: List of message objects
            collection_name: Collection name to store in

        Returns:
            Number of messages successfully stored
        """
        if not messages:
            return 0

        try:
            collection = self.get_collection(collection_name)

            # Prepare bulk operations
            operations = []
            for message in messages:
                # Skip messages without timestamp (required field)
                if not message.get('ts'):
                    logger.warning(f"Skipping message without timestamp: {message}")
                    continue

                message_doc = {
                    'ts': message['ts'],
                    'type': message.get('type', 'message'),
                    'user': message.get('user'),
                    'text': message.get('text', ''),
                    'thread_ts': message.get('thread_ts'),
                    'parent_user_id': message.get('parent_user_id'),
                    'reply_count': message.get('reply_count', 0),
                    'reply_users_count': message.get('reply_users_count', 0),
                    'latest_reply': message.get('latest_reply'),
                    'subscribed': message.get('subscribed'),
                    'last_read': message.get('last_read'),
                    'unread_count': message.get('unread_count', 0),
                    'blocks': message.get('blocks', []),
                    'attachments': message.get('attachments', []),
                    'files': message.get('files', []),
                    'reactions': message.get('reactions', []),
                    'edited': message.get('edited'),
                    'bot_id': message.get('bot_id'),
                    'app_id': message.get('app_id'),
                    'subtype': message.get('subtype'),
                    'hidden': message.get('hidden', False),
                    'deleted_ts': message.get('deleted_ts'),
                    'is_starred': message.get('is_starred', False)
                }

                # Use ReplaceOne operation object
                operations.append(
                    ReplaceOne(
                        filter={'ts': message['ts']},
                        replacement=message_doc,
                        upsert=True
                    )
                )

            # Execute bulk write only if we have operations
            if not operations:
                logger.warning(f"No valid operations to execute for {collection_name}")
                return 0

            result = collection.bulk_write(operations, ordered=False)

            logger.info(f"Batch stored {result.upserted_count + result.modified_count} messages in {collection_name}")
            return result.upserted_count + result.modified_count

        except errors.BulkWriteError as e:
            # Handle bulk write errors specifically
            logger.error(f"Bulk write error in {collection_name}: {e.details}")
            # Return the number of successful operations
            return e.details.get('nInserted', 0) + e.details.get('nModified', 0)
        except Exception as e:
            logger.error(f"Error in batch storing messages for {collection_name}: {str(e)}")
            return 0

    def store_users_list(self, users: List[Dict[str, Any]]) -> int:
        """
        Store all users in the users_list collection.

        Args:
            users: List of user objects from Slack API

        Returns:
            Number of users stored
        """
        if not users:
            return 0

        try:
            collection = self.get_collection('users_list')

            # Prepare bulk operations
            operations = []
            for user in users:
                # Skip users without ID (required field)
                if not user.get('id'):
                    logger.warning(f"Skipping user without ID: {user}")
                    continue

                user_doc = {
                    'id': user['id'],
                    'name': user.get('name'),
                    'real_name': user.get('real_name'),
                    'display_name': user.get('profile', {}).get('display_name'),
                    'email': user.get('profile', {}).get('email'),
                    'title': user.get('profile', {}).get('title'),
                    'phone': user.get('profile', {}).get('phone'),
                    'skype': user.get('profile', {}).get('skype'),
                    'image_24': user.get('profile', {}).get('image_24'),
                    'image_32': user.get('profile', {}).get('image_32'),
                    'image_48': user.get('profile', {}).get('image_48'),
                    'image_72': user.get('profile', {}).get('image_72'),
                    'image_192': user.get('profile', {}).get('image_192'),
                    'image_512': user.get('profile', {}).get('image_512'),
                    'is_admin': user.get('is_admin', False),
                    'is_owner': user.get('is_owner', False),
                    'is_primary_owner': user.get('is_primary_owner', False),
                    'is_restricted': user.get('is_restricted', False),
                    'is_ultra_restricted': user.get('is_ultra_restricted', False),
                    'is_bot': user.get('is_bot', False),
                    'is_app_user': user.get('is_app_user', False),
                    'deleted': user.get('deleted', False),
                    'tz': user.get('tz'),
                    'tz_label': user.get('tz_label'),
                    'tz_offset': user.get('tz_offset'),
                    'profile': user.get('profile', {}),
                    'updated': user.get('updated'),
                    'has_2fa': user.get('has_2fa'),
                    'locale': user.get('locale')
                }

                # Use ReplaceOne operation object
                operations.append(
                    ReplaceOne(
                        filter={'id': user['id']},
                        replacement=user_doc,
                        upsert=True
                    )
                )

            # Execute bulk write only if we have operations
            if not operations:
                logger.warning("No valid user operations to execute")
                return 0

            result = collection.bulk_write(operations, ordered=False)

            logger.info(f"Stored {result.upserted_count + result.modified_count} users in users_list collection")
            return result.upserted_count + result.modified_count

        except errors.BulkWriteError as e:
            logger.error(f"Bulk write error storing users: {e.details}")
            return e.details.get('nInserted', 0) + e.details.get('nModified', 0)
        except Exception as e:
            logger.error(f"Error storing users list: {str(e)}")
            return 0

    def store_conversations_list(self, conversations: List[Dict[str, Any]]) -> int:
        """
        Store all conversations in the conversations_list collection.

        Args:
            conversations: List of conversation objects from Slack API

        Returns:
            Number of conversations stored
        """
        if not conversations:
            return 0

        try:
            collection = self.get_collection('conversations_list')

            # Prepare bulk operations
            operations = []
            for conversation in conversations:
                # Skip conversations without ID (required field)
                if not conversation.get('id'):
                    logger.warning(f"Skipping conversation without ID: {conversation}")
                    continue

                conversation_doc = {
                    'id': conversation['id'],
                    'name': conversation.get('name'),
                    'is_channel': conversation.get('is_channel', False),
                    'is_group': conversation.get('is_group', False),
                    'is_im': conversation.get('is_im', False),
                    'is_mpim': conversation.get('is_mpim', False),
                    'is_private': conversation.get('is_private', False),
                    'created': conversation.get('created'),
                    'creator': conversation.get('creator'),
                    'is_archived': conversation.get('is_archived', False),
                    'is_general': conversation.get('is_general', False),
                    'unlinked': conversation.get('unlinked', 0),
                    'name_normalized': conversation.get('name_normalized'),
                    'is_shared': conversation.get('is_shared', False),
                    'is_ext_shared': conversation.get('is_ext_shared', False),
                    'is_org_shared': conversation.get('is_org_shared', False),
                    'pending_shared': conversation.get('pending_shared', []),
                    'is_pending_ext_shared': conversation.get('is_pending_ext_shared', False),
                    'is_member': conversation.get('is_member', False),
                    'is_open': conversation.get('is_open'),
                    'updated': conversation.get('updated'),
                    'topic': conversation.get('topic', {}),
                    'purpose': conversation.get('purpose', {}),
                    'previous_names': conversation.get('previous_names', []),
                    'num_members': conversation.get('num_members'),
                    'locale': conversation.get('locale'),
                    'priority': conversation.get('priority'),
                    'user': conversation.get('user'),  # For DMs
                    'is_user_deleted': conversation.get('is_user_deleted')  # For DMs
                }

                # Use ReplaceOne operation object
                operations.append(
                    ReplaceOne(
                        filter={'id': conversation['id']},
                        replacement=conversation_doc,
                        upsert=True
                    )
                )

            # Execute bulk write only if we have operations
            if not operations:
                logger.warning("No valid conversation operations to execute")
                return 0

            result = collection.bulk_write(operations, ordered=False)

            logger.info(f"Stored {result.upserted_count + result.modified_count} conversations in conversations_list collection")
            return result.upserted_count + result.modified_count

        except errors.BulkWriteError as e:
            logger.error(f"Bulk write error storing conversations: {e.details}")
            return e.details.get('nInserted', 0) + e.details.get('nModified', 0)
        except Exception as e:
            logger.error(f"Error storing conversations list: {str(e)}")
            return 0

    def collection_exists(self, collection_name: str) -> bool:
        """
        Check if a collection exists in the database.

        Args:
            collection_name: Name of the collection to check

        Returns:
            True if collection exists, False otherwise
        """
        try:
            if self.database is None:
                return False

            collection_names = self.database.list_collection_names()
            return collection_name in collection_names

        except Exception as e:
            logger.error(f"Error checking if collection {collection_name} exists: {str(e)}")
            return False

    def get_conversation_stats(self, collection_name: str) -> Dict[str, Any]:
        """
        Get statistics for a conversation collection.

        Args:
            collection_name: Collection name

        Returns:
            Dictionary with collection statistics
        """
        try:
            collection = self.get_collection(collection_name)

            stats = {
                'total_messages': collection.count_documents({}),
                'threaded_messages': collection.count_documents({'thread_ts': {'$exists': True}}),
                'messages_with_files': collection.count_documents({'files': {'$ne': []}}),
                'messages_with_reactions': collection.count_documents({'reactions': {'$ne': []}}),
                'bot_messages': collection.count_documents({'bot_id': {'$exists': True}})
            }

            return stats

        except Exception as e:
            logger.error(f"Error getting stats for {collection_name}: {str(e)}")
            return {}

    def close_connection(self):
        """Close MongoDB connection."""
        if self.client is not None:
            self.client.close()
            logger.info("MongoDB connection closed")

    def _determine_conversation_type(self, conversation: Dict[str, Any]) -> str:
        """
        Determine the type of conversation.

        Args:
            conversation: Conversation object

        Returns:
            String describing conversation type
        """
        if conversation.get('is_channel'):
            return 'public_channel'
        elif conversation.get('is_group'):
            return 'private_channel'
        elif conversation.get('is_mpim'):
            return 'mpim'
        elif conversation.get('is_im'):
            return 'im'
        else:
            return 'unknown'

    def __enter__(self):
        """Context manager entry."""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close_connection()
