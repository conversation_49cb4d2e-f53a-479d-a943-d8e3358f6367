[{"name": "general"}, {"name": "random"}, {"name": "well-beat"}, {"name": "innovation-team-รวม"}, {"name": "cila"}, {"name": "squad_wellhealth"}, {"name": "well-screening-her-will"}, {"name": "get-health-project"}, {"name": "thonburi-med-int"}, {"name": "project-manager"}, {"name": "invitrace-esports"}, {"name": "param-9-hospital"}, {"name": "i-live-well-project"}, {"name": "ai-testing"}, {"name": "phu<PERSON>ly"}, {"name": "slh-product-design-team"}, {"name": "share-cv-profile-for-recruit"}, {"name": "product-lead-initiative"}, {"name": "consulting-g5"}, {"name": "product-management"}, {"name": "5-head-of-operation"}, {"name": "announcement"}, {"name": "bd-po"}, {"name": "itele-mtl"}, {"name": "get-health-uat"}, {"name": "7-pm-project-managment"}, {"name": "get-health"}, {"name": "proj_telecare"}, {"name": "proj_slh-uat"}, {"name": "deep-listener"}, {"name": "discuss-welllife-step-improvement"}, {"name": "proj_ninnin"}, {"name": "rachvipa-mri-project"}, {"name": "board-games"}, {"name": "rmc-dev"}, {"name": "rachvipa-mri-dev"}, {"name": "well-life-toyota-uat"}, {"name": "proj_itele-udon"}, {"name": "healthup-sit-log"}, {"name": "healthup-dev-log"}, {"name": "well-health-log-dev"}, {"name": "well-health-log-prod"}, {"name": "team_digital-product-development"}, {"name": "proj_dhs"}, {"name": "system_dhs"}, {"name": "proj_dhs-design"}, {"name": "system_addwise"}, {"name": "proj_addwise-design"}, {"name": "proj_dhs-frontend"}, {"name": "proj_dhs-backend"}, {"name": "proj_team-dhs-provider-web"}, {"name": "proj_team-dhs-mobile"}, {"name": "proj_addwise"}, {"name": "proj_addwise-qa"}, {"name": "proj_dhs-qa"}, {"name": "proj_team_addwise-biz-web"}, {"name": "system_product"}, {"name": "team_product-feedback"}, {"name": "proj_team_addwise-provider-web"}, {"name": "proj_rmc"}, {"name": "proj_rmc-design"}, {"name": "proj_slh-design"}, {"name": "proj_slh"}, {"name": "proj_team_addwise-backoffice"}, {"name": "proj_addwise-frontend"}, {"name": "proj-pricing-bi-and-packages"}, {"name": "ilwsales-invitracexaji"}, {"name": "bay-pentest"}, {"name": "project_bdms_samui"}, {"name": "ext_bdms_samui"}, {"name": "system_bdms_samui"}, {"name": "dept_product"}, {"name": "proj_healthup"}, {"name": "proj_bplus_bdms"}, {"name": "เปิดท้าย-invitrace"}, {"name": "design-welllife"}, {"name": "predict_health"}, {"name": "healthup-release"}, {"name": "wellsport-"}, {"name": "proj_cgh"}, {"name": "proj_bcare"}, {"name": "team_product-development"}, {"name": "squad_wellhospital"}, {"name": "addwise-noti"}, {"name": "chatbot-test"}, {"name": "rmc-monitor"}, {"name": "dev-sm-support"}, {"name": "itele-notification"}, {"name": "testviewi"}, {"name": "i-tele-dev"}, {"name": "well-life-dev"}, {"name": "well-algorithm-dev"}, {"name": "aten-invitrace"}, {"name": "well-health-dev"}, {"name": "saint-<PERSON>ui<PERSON>-dev"}, {"name": "healthup-dev"}, {"name": "healthup-uat"}, {"name": "well-life-toyota-dev"}, {"name": "oceanlife-dev"}]