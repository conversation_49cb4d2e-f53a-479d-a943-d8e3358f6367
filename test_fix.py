#!/usr/bin/env python3
"""
Quick test to verify the MongoDB database checking fix.
"""
from mongo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_database_check():
    """Test that database checking works correctly."""
    print("Testing MongoDB database checking fix...")
    
    # Test 1: Check unconnected handler
    handler = <PERSON>goHandler()
    print(f"Before connection - database is None: {handler.database is None}")
    
    try:
        # This should raise an error
        handler.get_collection('test')
        print("❌ ERROR: Should have raised RuntimeError")
    except RuntimeError as e:
        print(f"✅ Correctly raised error: {e}")
    
    # Test 2: Check connected handler
    try:
        with <PERSON>goHandler() as connected_handler:
            print(f"After connection - database is None: {connected_handler.database is None}")
            
            if connected_handler.database is not None:
                print("✅ Database is properly connected")
                
                # Test collection access
                test_collection = connected_handler.get_collection('test_collection')
                print("✅ Collection access works")
            else:
                print("❌ Database is still None after connection")
                
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
    
    print("Test completed!")

if __name__ == "__main__":
    test_database_check()
