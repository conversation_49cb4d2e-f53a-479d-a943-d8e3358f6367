"""
Slack client module for conversation and message fetching
"""
import logging
import time
from typing import List, Dict, Any, Optional, Generator
from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError
from utils import retry_on_rate_limit
from config import (
    RATE_LIMIT_DELAY, MAX_RETRIES, CONVERSATIONS_LIMIT, 
    MESSAGES_LIMIT, INCLUDE_ARCHIVED, CONVERSATION_TYPES
)

logger = logging.getLogger(__name__)


class SlackConversationClient:
    """
    Handles Slack API calls for fetching conversations and messages.
    """
    
    def __init__(self, client: WebClient):
        """
        Initialize with authenticated Slack WebClient.
        
        Args:
            client: Authenticated Slack WebClient instance
        """
        self.client = client
        self.user_cache = {}  # Cache for user information
    
    @retry_on_rate_limit(max_retries=MAX_RETRIES, base_delay=RATE_LIMIT_DELAY)
    def get_conversations(self) -> Generator[Dict[str, Any], None, None]:
        """
        Fetch all conversations accessible to the user.
        
        Yields:
            Conversation objects from Slack API
        """
        logger.info("Fetching conversations...")
        cursor = None
        total_conversations = 0
        
        while True:
            try:
                # Add rate limiting delay
                time.sleep(RATE_LIMIT_DELAY)
                
                response = self.client.conversations_list(
                    types=','.join(CONVERSATION_TYPES),
                    exclude_archived=not INCLUDE_ARCHIVED,
                    limit=CONVERSATIONS_LIMIT,
                    cursor=cursor
                )
                
                if not response["ok"]:
                    logger.error(f"Failed to fetch conversations: {response.get('error', 'Unknown error')}")
                    break
                
                conversations = response.get("channels", [])
                total_conversations += len(conversations)
                
                logger.info(f"Fetched {len(conversations)} conversations (total: {total_conversations})")
                
                for conversation in conversations:
                    yield conversation
                
                # Check for pagination
                cursor = response.get("response_metadata", {}).get("next_cursor")
                if not cursor:
                    break
                    
            except SlackApiError as e:
                logger.error(f"Slack API error fetching conversations: {e}")
                break
            except Exception as e:
                logger.error(f"Unexpected error fetching conversations: {e}")
                break
        
        logger.info(f"Finished fetching conversations. Total: {total_conversations}")
    
    @retry_on_rate_limit(max_retries=MAX_RETRIES, base_delay=RATE_LIMIT_DELAY)
    def get_conversation_history(self, channel_id: str) -> Generator[Dict[str, Any], None, None]:
        """
        Fetch all messages from a conversation.
        
        Args:
            channel_id: Slack channel/conversation ID
        
        Yields:
            Message objects from Slack API
        """
        logger.debug(f"Fetching history for conversation {channel_id}")
        cursor = None
        total_messages = 0
        
        while True:
            try:
                # Add rate limiting delay
                time.sleep(RATE_LIMIT_DELAY)
                
                response = self.client.conversations_history(
                    channel=channel_id,
                    limit=MESSAGES_LIMIT,
                    cursor=cursor
                )
                
                if not response["ok"]:
                    logger.error(f"Failed to fetch history for {channel_id}: {response.get('error', 'Unknown error')}")
                    break
                
                messages = response.get("messages", [])
                total_messages += len(messages)
                
                logger.debug(f"Fetched {len(messages)} messages from {channel_id} (total: {total_messages})")
                
                for message in messages:
                    yield message
                
                # Check for pagination
                cursor = response.get("response_metadata", {}).get("next_cursor")
                if not cursor:
                    break
                    
            except SlackApiError as e:
                logger.error(f"Slack API error fetching history for {channel_id}: {e}")
                break
            except Exception as e:
                logger.error(f"Unexpected error fetching history for {channel_id}: {e}")
                break
        
        logger.debug(f"Finished fetching history for {channel_id}. Total messages: {total_messages}")
    
    @retry_on_rate_limit(max_retries=MAX_RETRIES, base_delay=RATE_LIMIT_DELAY)
    def get_thread_replies(self, channel_id: str, thread_ts: str) -> List[Dict[str, Any]]:
        """
        Fetch all replies in a thread.
        
        Args:
            channel_id: Slack channel/conversation ID
            thread_ts: Thread timestamp
        
        Returns:
            List of reply message objects
        """
        logger.debug(f"Fetching thread replies for {channel_id}:{thread_ts}")
        cursor = None
        all_replies = []
        
        while True:
            try:
                # Add rate limiting delay
                time.sleep(RATE_LIMIT_DELAY)
                
                response = self.client.conversations_replies(
                    channel=channel_id,
                    ts=thread_ts,
                    cursor=cursor
                )
                
                if not response["ok"]:
                    logger.error(f"Failed to fetch thread replies for {channel_id}:{thread_ts}: {response.get('error', 'Unknown error')}")
                    break
                
                messages = response.get("messages", [])
                
                # Skip the first message (parent) if this is the first page
                if cursor is None and messages:
                    messages = messages[1:]  # Remove parent message
                
                all_replies.extend(messages)
                
                # Check for pagination
                cursor = response.get("response_metadata", {}).get("next_cursor")
                if not cursor:
                    break
                    
            except SlackApiError as e:
                logger.error(f"Slack API error fetching thread replies for {channel_id}:{thread_ts}: {e}")
                break
            except Exception as e:
                logger.error(f"Unexpected error fetching thread replies for {channel_id}:{thread_ts}: {e}")
                break
        
        logger.debug(f"Fetched {len(all_replies)} thread replies for {channel_id}:{thread_ts}")
        return all_replies
    
    @retry_on_rate_limit(max_retries=MAX_RETRIES, base_delay=RATE_LIMIT_DELAY)
    def get_user_info(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user information with caching.
        
        Args:
            user_id: Slack user ID
        
        Returns:
            User information dictionary or None if not found
        """
        # Check cache first
        if user_id in self.user_cache:
            return self.user_cache[user_id]
        
        try:
            # Add rate limiting delay
            time.sleep(RATE_LIMIT_DELAY)
            
            response = self.client.users_info(user=user_id)
            
            if response["ok"]:
                user_info = response["user"]
                self.user_cache[user_id] = user_info
                return user_info
            else:
                logger.warning(f"Failed to fetch user info for {user_id}: {response.get('error', 'Unknown error')}")
                return None
                
        except SlackApiError as e:
            logger.warning(f"Slack API error fetching user info for {user_id}: {e}")
            return None
        except Exception as e:
            logger.warning(f"Unexpected error fetching user info for {user_id}: {e}")
            return None
    
    def get_conversation_members(self, channel_id: str) -> List[str]:
        """
        Get list of conversation members.
        
        Args:
            channel_id: Slack channel/conversation ID
        
        Returns:
            List of user IDs
        """
        try:
            # Add rate limiting delay
            time.sleep(RATE_LIMIT_DELAY)
            
            response = self.client.conversations_members(channel=channel_id)
            
            if response["ok"]:
                return response.get("members", [])
            else:
                logger.warning(f"Failed to fetch members for {channel_id}: {response.get('error', 'Unknown error')}")
                return []
                
        except SlackApiError as e:
            logger.warning(f"Slack API error fetching members for {channel_id}: {e}")
            return []
        except Exception as e:
            logger.warning(f"Unexpected error fetching members for {channel_id}: {e}")
            return []
    
    def clear_user_cache(self):
        """Clear the user information cache."""
        self.user_cache.clear()
        logger.debug("User cache cleared")
