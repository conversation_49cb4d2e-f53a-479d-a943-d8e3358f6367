#!/usr/bin/env python3
"""
Test script to verify Slack and MongoDB setup before running the full export.
"""
import sys
import logging
from auth import create_authenticated_client
from mongo_handler import MongoHandler
from utils import setup_logging

def test_slack_connection():
    """Test Slack authentication and permissions."""
    print("Testing Slack connection...")
    
    try:
        client, user_info = create_authenticated_client()
        print(f"✓ Successfully authenticated as: {user_info['user']} on team {user_info['team']}")
        
        # Test basic API calls
        print("Testing API permissions...")
        
        # Test conversations.list
        try:
            response = client.conversations_list(limit=1)
            if response["ok"]:
                print("✓ Conversations access: OK")
            else:
                print(f"✗ Conversations access failed: {response.get('error')}")
        except Exception as e:
            print(f"✗ Conversations access error: {str(e)}")
        
        # Test users.list
        try:
            response = client.users_list(limit=1)
            if response["ok"]:
                print("✓ Users access: OK")
            else:
                print(f"✗ Users access failed: {response.get('error')}")
        except Exception as e:
            print(f"✗ Users access error: {str(e)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Slack connection failed: {str(e)}")
        return False


def test_mongodb_connection():
    """Test MongoDB connection."""
    print("\nTesting MongoDB connection...")
    
    try:
        with MongoHandler() as mongo_handler:
            print(f"✓ Successfully connected to MongoDB")
            print(f"✓ Database: {mongo_handler.database_name}")
            
            # Test write permissions
            test_collection = mongo_handler.get_collection('_test_connection')
            test_doc = {'test': True, 'message': 'Connection test'}
            
            test_collection.insert_one(test_doc)
            print("✓ Write permissions: OK")
            
            # Clean up test document
            test_collection.delete_one({'test': True})
            print("✓ Delete permissions: OK")
            
            return True
            
    except Exception as e:
        print(f"✗ MongoDB connection failed: {str(e)}")
        return False


def test_environment_variables():
    """Test that required environment variables are set."""
    print("\nTesting environment variables...")
    
    try:
        from config import SLACK_USER_TOKEN, MONGODB_CONNECTION_STRING, MONGODB_DATABASE_NAME
        
        if SLACK_USER_TOKEN:
            print("✓ SLACK_USER_TOKEN is set")
        else:
            print("✗ SLACK_USER_TOKEN is not set")
            return False
        
        if MONGODB_CONNECTION_STRING:
            print(f"✓ MONGODB_CONNECTION_STRING is set: {MONGODB_CONNECTION_STRING}")
        else:
            print("✗ MONGODB_CONNECTION_STRING is not set")
            return False
        
        if MONGODB_DATABASE_NAME:
            print(f"✓ MONGODB_DATABASE_NAME is set: {MONGODB_DATABASE_NAME}")
        else:
            print("✗ MONGODB_DATABASE_NAME is not set")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ Environment variable error: {str(e)}")
        return False


def test_dependencies():
    """Test that required Python packages are installed."""
    print("\nTesting Python dependencies...")
    
    required_packages = [
        ('slack_sdk', 'Slack SDK'),
        ('pymongo', 'PyMongo'),
        ('dotenv', 'Python-dotenv')
    ]
    
    all_good = True
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"✓ {name} is installed")
        except ImportError:
            print(f"✗ {name} is not installed")
            all_good = False
    
    return all_good


def main():
    """Run all tests."""
    setup_logging('INFO')
    
    print("="*60)
    print("SLACK CHAT HISTORY EXPORTER - SETUP TEST")
    print("="*60)
    
    tests = [
        ("Environment Variables", test_environment_variables),
        ("Python Dependencies", test_dependencies),
        ("MongoDB Connection", test_mongodb_connection),
        ("Slack Connection", test_slack_connection),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * len(test_name))
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    all_passed = True
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"{test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "="*60)
    
    if all_passed:
        print("🎉 All tests passed! You're ready to run the export.")
        print("\nTo start the export, run:")
        print("python main.py")
    else:
        print("❌ Some tests failed. Please fix the issues above before running the export.")
        print("\nFor help, run:")
        print("python main.py --help")
    
    print("="*60)
    
    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
