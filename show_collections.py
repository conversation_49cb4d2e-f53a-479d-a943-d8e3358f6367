#!/usr/bin/env python3
"""
Simple script to show all MongoDB collections and their types.
"""
from mongo_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from utils import setup_logging

def show_all_collections():
    """Show all collections with detailed information."""
    print("📁 ALL MONGODB COLLECTIONS")
    print("="*60)
    
    try:
        with <PERSON><PERSON><PERSON>and<PERSON>() as mongo_handler:
            # Get all collections
            all_collections = mongo_handler.database.list_collection_names()
            
            if not all_collections:
                print("❌ No collections found in database")
                return
            
            print(f"Total collections: {len(all_collections)}\n")
            
            # Categorize collections
            dm_collections = []
            channel_collections = []
            metadata_collections = []
            other_collections = []
            
            for collection_name in sorted(all_collections):
                if collection_name.startswith('_'):
                    metadata_collections.append(collection_name)
                elif collection_name.startswith('dm_'):
                    dm_collections.append(collection_name)
                elif collection_name in ['users_list', 'conversations_list']:
                    other_collections.append(collection_name)
                else:
                    channel_collections.append(collection_name)
            
            # Show DM collections
            if dm_collections:
                print(f"💬 DIRECT MESSAGE COLLECTIONS ({len(dm_collections)}):")
                print("-" * 50)
                for dm_col in dm_collections:
                    stats = mongo_handler.get_conversation_stats(dm_col)
                    msg_count = stats.get('total_messages', 0)
                    threaded = stats.get('threaded_messages', 0)
                    print(f"  📱 {dm_col}")
                    print(f"     Messages: {msg_count} (Threaded: {threaded})")
                print()
            else:
                print("💬 DIRECT MESSAGE COLLECTIONS: None found")
                print("   ⚠️  This could mean:")
                print("   - No DM conversations exist")
                print("   - Token lacks 'im:read' and 'im:history' permissions")
                print("   - DMs were filtered out during export")
                print("   - Export hasn't been run yet")
                print()
            
            # Show channel collections
            if channel_collections:
                print(f"📺 CHANNEL COLLECTIONS ({len(channel_collections)}):")
                print("-" * 50)
                for chan_col in channel_collections[:10]:  # Show first 10
                    stats = mongo_handler.get_conversation_stats(chan_col)
                    msg_count = stats.get('total_messages', 0)
                    threaded = stats.get('threaded_messages', 0)
                    print(f"  📺 {chan_col}")
                    print(f"     Messages: {msg_count} (Threaded: {threaded})")
                
                if len(channel_collections) > 10:
                    print(f"  ... and {len(channel_collections) - 10} more channel collections")
                print()
            
            # Show metadata collections
            if metadata_collections:
                print(f"🗂️  METADATA COLLECTIONS ({len(metadata_collections)}):")
                print("-" * 50)
                for meta_col in metadata_collections:
                    collection = mongo_handler.get_collection(meta_col)
                    count = collection.count_documents({})
                    print(f"  🗂️  {meta_col}: {count} documents")
                print()
            
            # Show other collections
            if other_collections:
                print(f"📊 OTHER COLLECTIONS ({len(other_collections)}):")
                print("-" * 50)
                for other_col in other_collections:
                    collection = mongo_handler.get_collection(other_col)
                    count = collection.count_documents({})
                    print(f"  📊 {other_col}: {count} documents")
                print()
            
            # Check conversation metadata for DMs
            print("🔍 CHECKING CONVERSATION METADATA FOR DMs:")
            print("-" * 50)
            
            if '_conversation_metadata' in all_collections:
                metadata_collection = mongo_handler.get_collection('_conversation_metadata')
                
                # Count by conversation type
                types_count = {}
                for doc in metadata_collection.find():
                    conv_type = doc.get('conversation_type', 'unknown')
                    types_count[conv_type] = types_count.get(conv_type, 0) + 1
                
                for conv_type, count in types_count.items():
                    print(f"  {conv_type}: {count} conversations")
                
                # Show DM metadata specifically
                dm_metadata = list(metadata_collection.find({'conversation_type': 'im'}))
                if dm_metadata:
                    print(f"\n  📱 DM Metadata Entries ({len(dm_metadata)}):")
                    for dm_meta in dm_metadata[:5]:
                        conv_id = dm_meta.get('conversation_id', 'Unknown')
                        collection_name = dm_meta.get('collection_name', 'Unknown')
                        print(f"    {conv_id} -> {collection_name}")
                    
                    if len(dm_metadata) > 5:
                        print(f"    ... and {len(dm_metadata) - 5} more DM entries")
                else:
                    print("  ❌ No DM entries found in metadata")
            else:
                print("  ❌ No conversation metadata collection found")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")


def main():
    """Main function."""
    setup_logging('WARNING')  # Reduce log noise
    
    show_all_collections()
    
    print("\n" + "="*60)
    print("💡 TIPS:")
    print("="*60)
    print("1. DM collections are named like: dm_user1_user2")
    print("2. If no DM collections exist, run: python diagnose_dms.py")
    print("3. To view specific collection: python view_data.py view <collection_name>")
    print("4. To see conversation list: python view_data.py list")
    print("5. To see users: python view_data.py users")


if __name__ == "__main__":
    main()
