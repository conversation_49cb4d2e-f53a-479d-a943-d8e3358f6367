#!/usr/bin/env python3
"""
Simple script to view exported Slack data from MongoDB.
"""
import sys
from datetime import datetime
from mongo_handler import MongoHandler
from utils import setup_logging, format_timestamp


def list_conversations():
    """List all exported conversations."""
    print("Available Conversations:")
    print("="*50)
    
    with MongoHandler() as mongo_handler:
        # Get metadata
        metadata_collection = mongo_handler.get_collection('_conversation_metadata')
        conversations = list(metadata_collection.find().sort('name', 1))
        
        if not conversations:
            print("No conversations found. Run the export first.")
            return
        
        for conv in conversations:
            conv_type = conv.get('conversation_type', 'unknown')
            name = conv.get('name', conv.get('conversation_id', 'unnamed'))
            collection_name = conv.get('collection_name', 'unknown')
            
            # Get message count
            stats = mongo_handler.get_conversation_stats(collection_name)
            message_count = stats.get('total_messages', 0)
            
            print(f"📁 {name} ({conv_type})")
            print(f"   Collection: {collection_name}")
            print(f"   Messages: {message_count}")
            if conv.get('is_archived'):
                print("   Status: Archived")
            print()


def view_conversation(collection_name: str, limit: int = 10):
    """View messages from a specific conversation."""
    print(f"Messages from '{collection_name}' (showing last {limit}):")
    print("="*80)
    
    with MongoHandler() as mongo_handler:
        collection = mongo_handler.get_collection(collection_name)
        
        # Get recent messages
        messages = list(collection.find().sort('ts', -1).limit(limit))
        
        if not messages:
            print("No messages found in this conversation.")
            return
        
        # Reverse to show chronologically
        messages.reverse()
        
        for msg in messages:
            timestamp = format_timestamp(msg.get('ts', ''))
            user = msg.get('user', 'Unknown')
            text = msg.get('text', '')
            
            # Handle different message types
            if msg.get('subtype'):
                print(f"[{timestamp}] ({msg['subtype']}) {text}")
            else:
                print(f"[{timestamp}] {user}: {text}")
            
            # Show thread replies if any
            if msg.get('thread_replies'):
                replies = msg['thread_replies']
                print(f"   └─ Thread with {len(replies)} replies:")
                for reply in replies[:3]:  # Show first 3 replies
                    reply_time = format_timestamp(reply.get('ts', ''))
                    reply_user = reply.get('user', 'Unknown')
                    reply_text = reply.get('text', '')
                    print(f"   [{reply_time}] {reply_user}: {reply_text}")
                if len(replies) > 3:
                    print(f"   ... and {len(replies) - 3} more replies")
            
            print()


def show_stats():
    """Show overall export statistics."""
    print("Export Statistics:")
    print("="*50)
    
    with MongoHandler() as mongo_handler:
        collections = mongo_handler.database.list_collection_names()
        message_collections = [c for c in collections if not c.startswith('_')]
        
        total_messages = 0
        total_threads = 0
        total_files = 0
        total_reactions = 0
        
        print(f"Total conversations: {len(message_collections)}")
        print()
        
        for collection_name in message_collections:
            stats = mongo_handler.get_conversation_stats(collection_name)
            if stats:
                messages = stats.get('total_messages', 0)
                threads = stats.get('threaded_messages', 0)
                files = stats.get('messages_with_files', 0)
                reactions = stats.get('messages_with_reactions', 0)
                
                total_messages += messages
                total_threads += threads
                total_files += files
                total_reactions += reactions
                
                print(f"📁 {collection_name}:")
                print(f"   Messages: {messages}")
                if threads > 0:
                    print(f"   Threaded: {threads}")
                if files > 0:
                    print(f"   With files: {files}")
                if reactions > 0:
                    print(f"   With reactions: {reactions}")
                print()
        
        print("="*50)
        print(f"TOTALS:")
        print(f"Messages: {total_messages}")
        print(f"Threaded messages: {total_threads}")
        print(f"Messages with files: {total_files}")
        print(f"Messages with reactions: {total_reactions}")


def search_messages(query: str, limit: int = 20):
    """Search for messages containing specific text."""
    print(f"Searching for '{query}' (showing up to {limit} results):")
    print("="*80)
    
    with MongoHandler() as mongo_handler:
        collections = mongo_handler.database.list_collection_names()
        message_collections = [c for c in collections if not c.startswith('_')]
        
        results_found = 0
        
        for collection_name in message_collections:
            if results_found >= limit:
                break
                
            collection = mongo_handler.get_collection(collection_name)
            
            # Search in message text
            messages = collection.find({
                'text': {'$regex': query, '$options': 'i'}
            }).sort('ts', -1).limit(limit - results_found)
            
            for msg in messages:
                if results_found >= limit:
                    break
                    
                timestamp = format_timestamp(msg.get('ts', ''))
                user = msg.get('user', 'Unknown')
                text = msg.get('text', '')
                
                print(f"📁 {collection_name}")
                print(f"[{timestamp}] {user}: {text}")
                print()
                
                results_found += 1
        
        if results_found == 0:
            print("No messages found matching your search.")
        else:
            print(f"Found {results_found} results.")


def main():
    """Main function with command-line interface."""
    setup_logging('WARNING')  # Reduce log noise
    
    if len(sys.argv) < 2:
        print("Slack Chat History Viewer")
        print("="*30)
        print("Usage:")
        print("  python view_data.py list                    - List all conversations")
        print("  python view_data.py stats                   - Show export statistics")
        print("  python view_data.py view <collection> [n]   - View messages from collection")
        print("  python view_data.py search <query> [n]      - Search for messages")
        print()
        print("Examples:")
        print("  python view_data.py list")
        print("  python view_data.py view general 20")
        print("  python view_data.py search 'project update' 10")
        return
    
    command = sys.argv[1].lower()
    
    try:
        if command == 'list':
            list_conversations()
        
        elif command == 'stats':
            show_stats()
        
        elif command == 'view':
            if len(sys.argv) < 3:
                print("Error: Please specify a collection name")
                print("Use 'python view_data.py list' to see available collections")
                return
            
            collection_name = sys.argv[2]
            limit = int(sys.argv[3]) if len(sys.argv) > 3 else 10
            view_conversation(collection_name, limit)
        
        elif command == 'search':
            if len(sys.argv) < 3:
                print("Error: Please specify a search query")
                return
            
            query = sys.argv[2]
            limit = int(sys.argv[3]) if len(sys.argv) > 3 else 20
            search_messages(query, limit)
        
        else:
            print(f"Unknown command: {command}")
            print("Use 'python view_data.py' for usage information")
    
    except Exception as e:
        print(f"Error: {str(e)}")
        print("Make sure you have run the export first and MongoDB is accessible.")


if __name__ == "__main__":
    main()
