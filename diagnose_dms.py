#!/usr/bin/env python3
"""
Diagnostic script to troubleshoot missing direct message collections.
"""
import sys
from auth import create_authenticated_client
from mongo_handler import <PERSON>goH<PERSON><PERSON>
from utils import setup_logging, sanitize_collection_name, create_dm_collection_name

def diagnose_slack_permissions():
    """Test Slack API permissions for DMs."""
    print("🔍 Diagnosing Slack API Permissions for DMs...")
    print("="*60)
    
    try:
        client, user_info = create_authenticated_client()
        print(f"✅ Authenticated as: {user_info['user']}")
        
        # Test conversations.list with IM type
        print("\n📋 Testing conversations.list for IMs...")
        try:
            response = client.conversations_list(types='im', limit=10)
            if response["ok"]:
                ims = response.get("channels", [])
                print(f"✅ Found {len(ims)} direct message conversations")
                
                if ims:
                    print("\n📝 Sample DM conversations:")
                    for i, im in enumerate(ims[:5], 1):
                        user_id = im.get('user', 'Unknown')
                        im_id = im.get('id', 'Unknown')
                        is_open = im.get('is_open', False)
                        print(f"  {i}. DM with user {user_id} (ID: {im_id}, Open: {is_open})")
                else:
                    print("⚠️  No DM conversations found. This could mean:")
                    print("   - You haven't had any recent DM conversations")
                    print("   - DMs are not accessible with current token")
                    print("   - All DMs are closed/archived")
                
                return ims
            else:
                print(f"❌ conversations.list failed: {response.get('error')}")
                return []
                
        except Exception as e:
            print(f"❌ Error testing conversations.list: {str(e)}")
            return []
            
    except Exception as e:
        print(f"❌ Authentication failed: {str(e)}")
        return []


def diagnose_dm_history(client, im_conversations):
    """Test fetching history from DM conversations."""
    print("\n💬 Testing DM History Access...")
    print("="*40)
    
    if not im_conversations:
        print("⚠️  No DM conversations to test")
        return
    
    for i, im in enumerate(im_conversations[:3], 1):  # Test first 3 DMs
        im_id = im.get('id')
        user_id = im.get('user', 'Unknown')
        
        print(f"\n{i}. Testing DM {im_id} with user {user_id}...")
        
        try:
            response = client.conversations_history(channel=im_id, limit=5)
            if response["ok"]:
                messages = response.get("messages", [])
                print(f"   ✅ Found {len(messages)} messages")
                
                if messages:
                    latest_msg = messages[0]
                    msg_text = latest_msg.get('text', '')[:50] + '...' if len(latest_msg.get('text', '')) > 50 else latest_msg.get('text', '')
                    print(f"   📄 Latest message: {msg_text}")
                else:
                    print("   ⚠️  No messages in this DM")
            else:
                print(f"   ❌ Failed to get history: {response.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Error getting history: {str(e)}")


def diagnose_user_info(client, im_conversations):
    """Test getting user information for DM participants."""
    print("\n👤 Testing User Information Access...")
    print("="*40)
    
    if not im_conversations:
        print("⚠️  No DM conversations to test")
        return
    
    for i, im in enumerate(im_conversations[:3], 1):
        user_id = im.get('user')
        if not user_id:
            continue
            
        print(f"\n{i}. Getting info for user {user_id}...")
        
        try:
            response = client.users_info(user=user_id)
            if response["ok"]:
                user = response["user"]
                name = user.get('name', 'Unknown')
                real_name = user.get('real_name', '')
                display_name = user.get('profile', {}).get('display_name', '')
                
                print(f"   ✅ User: {name}")
                if real_name:
                    print(f"   📝 Real name: {real_name}")
                if display_name:
                    print(f"   📝 Display name: {display_name}")
                
                # Test collection naming
                mock_participants = [user]
                collection_name = create_dm_collection_name(mock_participants)
                print(f"   📁 Would create collection: {collection_name}")
                
            else:
                print(f"   ❌ Failed to get user info: {response.get('error')}")
                
        except Exception as e:
            print(f"   ❌ Error getting user info: {str(e)}")


def diagnose_mongodb_collections():
    """Check what collections exist in MongoDB."""
    print("\n🗄️  Diagnosing MongoDB Collections...")
    print("="*40)
    
    try:
        with MongoHandler() as mongo_handler:
            collections = mongo_handler.database.list_collection_names()
            
            # Filter for DM collections
            dm_collections = [c for c in collections if c.startswith('dm_')]
            im_collections = [c for c in collections if 'im_' in c.lower()]
            
            print(f"📊 Total collections: {len(collections)}")
            print(f"📊 DM collections (dm_*): {len(dm_collections)}")
            print(f"📊 IM collections (*im*): {len(im_collections)}")
            
            if dm_collections:
                print("\n📁 Found DM collections:")
                for dm_col in dm_collections[:10]:  # Show first 10
                    stats = mongo_handler.get_conversation_stats(dm_col)
                    msg_count = stats.get('total_messages', 0)
                    print(f"   - {dm_col}: {msg_count} messages")
            else:
                print("\n⚠️  No DM collections found")
            
            if im_collections:
                print("\n📁 Found IM-related collections:")
                for im_col in im_collections[:10]:
                    stats = mongo_handler.get_conversation_stats(im_col)
                    msg_count = stats.get('total_messages', 0)
                    print(f"   - {im_col}: {msg_count} messages")
            
            # Check metadata for DMs
            print("\n🔍 Checking conversation metadata for DMs...")
            metadata_collection = mongo_handler.get_collection('_conversation_metadata')
            dm_metadata = list(metadata_collection.find({'conversation_type': 'im'}))
            
            print(f"📊 DM entries in metadata: {len(dm_metadata)}")
            
            if dm_metadata:
                print("\n📝 DM metadata entries:")
                for dm_meta in dm_metadata[:5]:
                    conv_id = dm_meta.get('conversation_id', 'Unknown')
                    collection_name = dm_meta.get('collection_name', 'Unknown')
                    print(f"   - {conv_id} -> {collection_name}")
            
            return dm_collections, im_collections
            
    except Exception as e:
        print(f"❌ MongoDB diagnosis failed: {str(e)}")
        return [], []


def provide_recommendations(im_conversations, dm_collections):
    """Provide recommendations based on diagnosis."""
    print("\n💡 Recommendations:")
    print("="*20)
    
    if not im_conversations:
        print("🔧 No DM conversations found from Slack API:")
        print("   1. Check token scopes include 'im:read' and 'im:history'")
        print("   2. Verify you have recent DM conversations")
        print("   3. Check if DMs are being filtered by INCLUDE_CONVERSATIONS/EXCLUDE_CONVERSATIONS")
        print("   4. Try starting a DM conversation and run export again")
    
    elif not dm_collections:
        print("🔧 DM conversations found in Slack but not in MongoDB:")
        print("   1. Check export logs for errors during DM processing")
        print("   2. Verify CONVERSATION_TYPES includes 'im'")
        print("   3. Run export with DEBUG logging: LOG_LEVEL=DEBUG")
        print("   4. Check if SKIP_EXISTING_CONVERSATIONS is preventing re-export")
    
    else:
        print("✅ DM collections found! They might be named differently than expected.")
        print("   Use 'python view_data.py list' to see all conversations")


def main():
    """Run complete DM diagnosis."""
    setup_logging('WARNING')  # Reduce noise
    
    print("🔍 DIRECT MESSAGE DIAGNOSIS")
    print("="*60)
    print("This script will help diagnose why DM collections might be missing.\n")
    
    # Step 1: Test Slack permissions
    im_conversations = diagnose_slack_permissions()
    
    if im_conversations:
        # Step 2: Test DM history access
        try:
            client, _ = create_authenticated_client()
            diagnose_dm_history(client, im_conversations)
            diagnose_user_info(client, im_conversations)
        except Exception as e:
            print(f"❌ Could not test DM access: {str(e)}")
    
    # Step 3: Check MongoDB
    dm_collections, im_collections = diagnose_mongodb_collections()
    
    # Step 4: Provide recommendations
    provide_recommendations(im_conversations, dm_collections)
    
    print("\n" + "="*60)
    print("DIAGNOSIS COMPLETE")
    print("="*60)
    
    if im_conversations and dm_collections:
        print("🎉 DMs appear to be working correctly!")
    elif im_conversations:
        print("⚠️  DMs accessible via API but not stored in MongoDB")
    else:
        print("❌ No DMs accessible via Slack API")
    
    print("\nNext steps:")
    print("1. Review the recommendations above")
    print("2. Check your .env configuration")
    print("3. Run export with DEBUG logging if needed")
    print("4. Use 'python view_data.py list' to see all collections")


if __name__ == "__main__":
    main()
